## Overview

This Nx + pnpm monorepo contains multiple Next.js apps and React libraries.

- Apps: apps/agent-portal, apps/bot-builder, apps/enterprise-admin
- Libraries: libs/ui (shadcn-based UI kit), libs/shared (types, redux, etc.)

## Import conventions (absolute paths that work with VSCode)

There are two kinds of absolute imports you’ll use:

1) App-local absolute imports using the @ alias
- Usage: import from your app’s src with "@/..."
- Enabled per-app in each tsconfig.json

Example (inside any app):
<augment_code_snippet mode="EXCERPT">
````ts
import { Page } from '@/views/Page';
import { format } from '@/utils/format';
````
</augment_code_snippet>

2) Cross-package imports using workspace package names
- UI components, utils, styles: import from @b6ai/ui
- Shared types, redux, etc.: import from @b6ai/shared

Examples:
<augment_code_snippet mode="EXCERPT">
````ts
import { Button, Input } from '@b6ai/ui';
import '@b6ai/ui/styles/globals.css';
import { store, SomeType } from '@b6ai/shared';
````
</augment_code_snippet>

Important: Do NOT use @/components/ui/* or @/lib/* to reach into libs/ui. Those legacy aliases were removed because they conflicted with the app-local @ alias and broke VSCode auto-imports.

## What was fixed and why VSCode auto-imports now work

- Removed conflicting aliases from tsconfig.base.json:
  - Deleted @/* at the workspace root
  - Deleted @/components/ui/* and @/lib/* pointing into libs/ui
- Added per-app mapping for @ to each app’s tsconfig.json so that @ refers to that app’s ./src
- Pointed @b6ai/ui and @b6ai/shared to their source files for better editor type navigation

This makes module resolution unambiguous:
- @/... always means "this app’s src"
- @b6ai/ui and @b6ai/shared always mean the libraries

VSCode/TypeScript can now reliably suggest and insert absolute imports without falling back to long relative paths.

## File structure quick reference

- apps/<app-name>/src/...  App source code
- libs/ui/src/...          Shadcn-based UI components and utilities
- libs/shared/src/...      Shared types/state/utils

## TypeScript configuration (high-level)

- Root tsconfig.base.json
  - "baseUrl": "." (workspace root)
  - Library paths:
    - "@b6ai/ui" -> libs/ui/src/index.ts
    - "@b6ai/ui/*" -> libs/ui/src/*
    - "@b6ai/shared" -> libs/shared/src/index.ts
    - "@b6ai/shared/*" -> libs/shared/src/lib/*
- App tsconfig.json (each app)
  - Adds "paths": { "@/*": ["./src/*"] }
  - Next plugin via { "plugins": [{ "name": "next" }] }

## Next.js usage with workspace libraries

Import UI and shared code by package name. Example in an app component:
<augment_code_snippet mode="EXCERPT">
````tsx
import { Button } from '@b6ai/ui';
import '@b6ai/ui/styles/globals.css';
import { store } from '@b6ai/shared';

export default function Example() {
  return <Button>Click</Button>;
}
````
</augment_code_snippet>

Tip: If you run into transpilation issues with local packages on new Next versions, enable transpilePackages in your app’s next.config.js:
<augment_code_snippet mode="EXCERPT">
````js
// next.config.js
module.exports = {
  experimental: {
    transpilePackages: ['@b6ai/ui', '@b6ai/shared'],
  },
};
````
</augment_code_snippet>

## VSCode setup tips

- Use the workspace TypeScript version:
  - Command Palette → TypeScript: Select TypeScript Version → Use Workspace Version
- Optional preferences that help auto-imports:
  - "typescript.preferences.importModuleSpecifier": "non-relative"
  - "typescript.preferences.includePackageJsonAutoImports": "on"
- After tsconfig changes, restart TS Server:
  - Command Palette → TypeScript: Restart TS server

## Common pitfalls and how to avoid them

- Don’t reach into libs with the @ alias
  - Bad: import { Button } from '@/components/ui/button'
  - Good: import { Button } from '@b6ai/ui'
- Keep @ scoped to each app’s src
  - If auto-import offers relative paths, you may need to Restart TS server
- Avoid duplicate or conflicting path aliases in tsconfig files

## Building and running

- Dev a specific app: pnpm nx dev <app>
- Build a specific app: pnpm nx build <app>
- Test (where configured): pnpm nx test <project>
- See project targets: pnpm nx show project <project>

Examples:
<augment_code_snippet mode="EXCERPT">
````sh
pnpm nx dev enterprise-admin
pnpm nx dev agent-portal
pnpm nx dev bot-builder
````
</augment_code_snippet>

## Notes on pnpm workspace

The repo is managed with pnpm and pnpm-workspace.yaml. Apps and libs are real workspace packages, so imports like @b6ai/ui resolve via workspace symlinks and package.json exports. No extra webpack aliasing is needed.

## Next steps (optional)

- Consider removing the standalone 'libs' entry from pnpm-workspace.yaml (the 'libs/*' entry already covers all libraries)
- If you want a custom alias for libs/ui internals during development (without publishing subpaths), add one like "@ui/*": ["libs/ui/src/*"] in tsconfig.base.json. Prefer importing from @b6ai/ui public exports for stability.

