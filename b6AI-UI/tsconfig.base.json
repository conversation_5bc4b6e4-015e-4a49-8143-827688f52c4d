{"compilerOptions": {"composite": true, "declarationMap": true, "emitDeclarationOnly": true, "importHelpers": true, "isolatedModules": true, "lib": ["es2022", "DOM"], "module": "esnext", "moduleResolution": "bundler", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": true, "skipLibCheck": true, "strict": true, "target": "es2022", "customConditions": ["development"], "baseUrl": ".", "paths": {"@b6ai/shared": ["libs/shared/src/index.ts"], "@b6ai/shared/*": ["libs/shared/src/*"], "@b6ai/ui": ["libs/ui/src/index.ts"], "@b6ai/ui/*": ["libs/ui/src/*"]}}}