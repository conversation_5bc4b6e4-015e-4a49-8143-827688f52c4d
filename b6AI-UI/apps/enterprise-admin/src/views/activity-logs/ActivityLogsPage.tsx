import React, { useState, useMemo } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import {
  EyeIcon,
  KeyIcon,
  UserIcon,
  MessageSquareIcon,
  AlertTriangleIcon,
  SettingsIcon,
  ServerIcon,
} from 'lucide-react';
import {
  DataTable,
  TableFilters,
  createTextColumn,
  createDateColumn,
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbPage,
} from '@b6ai/ui';
import {
  useGetActivityLogsQuery,
  useExportActivityLogsMutation,
  ActivityLog,
  ActivityLogFilters,
  getEventTypeColor,
  useAuditLogger,
} from '@b6ai/shared';

export const ActivityLogsPage: React.FC = () => {
  const { logDataAccess } = useAuditLogger();

  // State for filters and pagination
  const [filters, setFilters] = useState<ActivityLogFilters>({});
  const [searchValue, setSearchValue] = useState('');
  const [page] = useState(1);
  const [pageSize] = useState(10);

  // API calls
  const {
    data: activityLogsData,
    isLoading,
    refetch,
  } = useGetActivityLogsQuery({
    page,
    pageSize,
    filters: {
      ...filters,
      search: searchValue || undefined,
    },
    sortBy: 'timestamp',
    sortOrder: 'desc',
  });

  const [exportLogs] = useExportActivityLogsMutation();

  // Event type filter options
  const eventTypeFilters = [
    { label: 'All Logs', value: 'all' },
    { label: 'Authentication', value: 'auth' },
    { label: 'User Management', value: 'user' },
    { label: 'Settings Changes', value: 'settings' },
    { label: 'Chat Events', value: 'chat' },
    { label: 'System Events', value: 'system' },
    { label: 'Alerts', value: 'alert' },
  ];

  // Handle filter changes
  const handleEventTypeFilter = (eventType: string) => {
    setFilters((prev) => ({
      ...prev,
      eventType: eventType === 'all' ? undefined : eventType,
    }));
  };

  const handleDateRangeChange = (range: { start: string; end: string }) => {
    setFilters((prev) => ({
      ...prev,
      startDate: range.start || undefined,
      endDate: range.end || undefined,
    }));
  };

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
  };

  // Handle export
  const handleExport = async () => {
    try {
      // Log export action
      await logDataAccess(
        'Activity Logs Exported',
        'User exported activity logs',
        {
          filters,
          searchValue,
          recordCount: activityLogsData?.total || 0,
        }
      );

      const blob = await exportLogs({
        filters: {
          ...filters,
          search: searchValue || undefined,
        },
      }).unwrap();

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `activity-logs-${
        new Date().toISOString().split('T')[0]
      }.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  // Get event type icon component
  const getEventTypeIconComponent = (type: ActivityLog['eventType']) => {
    const iconProps = { size: 16 };
    const colorClass = `text-${getEventTypeColor(type)}-500`;

    switch (type) {
      case 'auth':
        return <KeyIcon {...iconProps} className={colorClass} />;
      case 'user':
        return <UserIcon {...iconProps} className={colorClass} />;
      case 'settings':
        return <SettingsIcon {...iconProps} className={colorClass} />;
      case 'system':
        return <ServerIcon {...iconProps} className={colorClass} />;
      case 'chat':
        return <MessageSquareIcon {...iconProps} className={colorClass} />;
      case 'bot':
        return <MessageSquareIcon {...iconProps} className={colorClass} />;
      case 'alert':
        return <AlertTriangleIcon {...iconProps} className={colorClass} />;
      default:
        return <EyeIcon {...iconProps} className="text-gray-500" />;
    }
  };

  // Define table columns
  const columns: ColumnDef<ActivityLog>[] = useMemo(
    () => [
      createDateColumn('timestamp', 'Timestamp', 'long'),
      createTextColumn('user', 'User'),
      {
        accessorKey: 'event',
        header: 'Event',
        cell: ({ row }) => {
          const log = row.original;
          return (
            <div className="flex items-center">
              <span className="mr-2">
                {getEventTypeIconComponent(log.eventType)}
              </span>
              {log.event}
            </div>
          );
        },
      },
      createTextColumn('ipAddress', 'IP Address'),
      createTextColumn('details', 'Details'),
    ],
    []
  );

  // Active filters for display
  const activeFilters: Array<{
    label: string;
    value: string;
    onRemove: () => void;
  }> = [];

  if (filters.eventType) {
    const filterOption = eventTypeFilters.find(
      (f) => f.value === filters.eventType
    );
    if (filterOption) {
      activeFilters.push({
        label: 'Event Type',
        value: filterOption.label,
        onRemove: () => handleEventTypeFilter('all'),
      });
    }
  }

  if (filters.startDate && filters.endDate) {
    activeFilters.push({
      label: 'Date Range',
      value: `${filters.startDate} to ${filters.endDate}`,
      onRemove: () => handleDateRangeChange({ start: '', end: '' }),
    });
  }

  // Custom filters component
  const customFilters = (
    <TableFilters
      filterOptions={eventTypeFilters.map((filter) => ({
        label: filter.label,
        value: filter.value,
        active:
          filter.value === 'all'
            ? !filters.eventType
            : filters.eventType === filter.value,
        onClick: () => handleEventTypeFilter(filter.value),
      }))}
      dateRange={{
        start: filters.startDate || '',
        end: filters.endDate || '',
      }}
      onDateRangeChange={handleDateRangeChange}
      activeFilters={activeFilters}
    />
  );

  return (
    <div className="w-full p-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbPage>Activity Logs</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="mb-6 mt-4">
        <h1 className="text-2xl font-semibold text-light-foreground dark:text-dark-foreground">
          Activity Logs
        </h1>
        <p className="text-sm text-light-muted-foreground dark:text-dark-muted-foreground">
          Track user actions and system events for security and compliance
          purposes
        </p>
      </div>

      {/* Filters */}
      <div className="bg-light-card dark:bg-dark-card rounded-lg border border-light-border dark:border-dark-border p-4 mb-6">
        {customFilters}
      </div>

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={activityLogsData?.data || []}
        searchPlaceholder="Search by user, event, IP address..."
        searchValue={searchValue}
        onSearchChange={handleSearchChange}
        onRefresh={refetch}
        onExport={handleExport}
        isLoading={isLoading}
        emptyMessage="No activity logs found."
        pageSize={pageSize}
        enablePagination={true}
        enableSorting={true}
        enableSearch={true}
      />
    </div>
  );
};
