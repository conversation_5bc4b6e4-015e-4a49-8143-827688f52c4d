import { BILLING_PERIOD } from '@b6ai/shared';
import { cn } from '@b6ai/ui';
import { motion } from 'framer-motion';

interface ToggleProps {
  selected: BILLING_PERIOD;
  onChange: (value: BILLING_PERIOD) => void;
  className?: string;
}

export function PlanToggle({ selected, onChange, className }: ToggleProps) {
  return (
    <div
      className={cn('inline-flex items-center justify-center w-80', className)}
    >
      <div className="relative flex items-center bg-muted rounded-full p-1 shadow-inner w-full max-w-md overflow-hidden">
        {/* Animated background highlight */}
        <motion.div
          className="absolute top-1 h-[calc(100%-0.5rem)] w-[calc(50%-0.25rem)] bg-b6ai-gradient rounded-full shadow-md z-0"
          animate={{
            x: selected === BILLING_PERIOD.MONTHLY ? '100%' : 0,
          }}
          transition={{
            type: 'spring',
            stiffness: 400,
            damping: 28,
          }}
        />

        {/* Yearly Button */}
        <button
          type="button"
          onClick={() => onChange(BILLING_PERIOD.YEARLY)}
          className={cn(
            'relative z-10 flex items-center justify-center gap-2 px-4 py-2 rounded-full w-1/2 text-sm sm:text-base font-medium transition-colors duration-200',
            'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
            selected === BILLING_PERIOD.YEARLY
              ? 'btn-b6ai'
              : '!text-muted-foreground hover:!text-foreground hover:!bg-transparent'
          )}
        >
          <span>Yearly</span>
        </button>

        {/* Monthly Button */}
        <button
          type="button"
          onClick={() => onChange(BILLING_PERIOD.MONTHLY)}
          className={cn(
            'relative z-10 flex items-center justify-center gap-2 px-4 py-2 rounded-full w-1/2 text-sm sm:text-base font-medium transition-colors duration-200',
            'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
            selected === BILLING_PERIOD.MONTHLY
              ? 'btn-b6ai'
              : '!text-muted-foreground hover:!text-foreground hover:!bg-transparent'
          )}
        >
          <span>Monthly</span>
        </button>
      </div>
    </div>
  );
}
