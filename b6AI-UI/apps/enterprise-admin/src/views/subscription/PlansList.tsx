import {
  CURRENCY_SYMBOLS,
  Plan,
  PlanFeature,
  BILLING_PERIOD,
  TRIAL_PERIOD,
  TRIAL_PERIOD_UNIT,
} from '@b6ai/shared';
import {
  ArrowRightIcon,
  CheckIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  Loader,
  PackageIcon,
  PhoneIncoming,
  RocketIcon,
  ZapIcon,
} from 'lucide-react';
import { SetStateAction, Dispatch, useState, useEffect, useMemo } from 'react';
import { PlanToggle } from './PlanToggle';
import { getDifferentialFeatures } from '../../utils';
import { Button } from '@b6ai/ui';

const planIcons = [
  { Icon: PackageIcon, color: 'text-blue-500' },
  { Icon: ZapIcon, color: 'text-purple-500' },
  { Icon: RocketIcon, color: 'text-indigo-500' },
];
interface PlanListProps {
  plans: Plan[];
  reccommendedPlanCode?: string; // Optional: mark a plan as popular
  onGetStarted?: (plan: Plan) => void;
  onBookCall?: (plan: Plan) => void;
  billingPeriod: BILLING_PERIOD;
  setBillingPeriod: Dispatch<SetStateAction<BILLING_PERIOD>>;
  isLoading: boolean;
  selectedPlan: Plan | null;
}

export function PlanList({
  plans,
  reccommendedPlanCode,
  onGetStarted,
  onBookCall,
  billingPeriod,
  setBillingPeriod,
  isLoading,
  selectedPlan,
}: PlanListProps) {
  const [showFullFeatures, setShowFullFeatures] = useState(false);
  const [visibleFeatures, setVisibleFeatures] = useState(3);

  useEffect(() => {
    // Determine number of features to show based on viewport height
    const calculateVisibleFeatures = () => {
      const viewportHeight = window.innerHeight;
      // Adjust number of visible features based on viewport height
      if (viewportHeight < 700) {
        setVisibleFeatures(2);
      } else if (viewportHeight < 800) {
        setVisibleFeatures(3);
      } else {
        setVisibleFeatures(4);
      }
    };
    calculateVisibleFeatures();
    window.addEventListener('resize', calculateVisibleFeatures);
    return () => {
      window.removeEventListener('resize', calculateVisibleFeatures);
    };
  }, []);

  const formatPrice = (priceCents: number, currency: string) => {
    const symbol = CURRENCY_SYMBOLS[currency] || currency;
    const price = (priceCents / 100).toFixed(2);
    // Remove .00 for cleaner look
    return `${symbol}${price.replace(/\.00$/, '')}`;
  };

  const handleAction = (plan: Plan) => {
    if (plan.planCode === 'ENTERPRISE' && onBookCall) {
      onBookCall(plan);
    } else if (onGetStarted) {
      onGetStarted(plan);
    }
  };

  function getIcon(idx: number) {
    const iconData = planIcons[idx] ?? {
      Icon: RocketIcon,
      color: 'text-indigo-500',
    };
    const { Icon, color } = iconData;
    return <Icon size={24} className={color} />;
  }

  const allFeatures: PlanFeature[] = useMemo(() => {
    return Array.from(
      new Set(plans.flatMap((plan) => plan.planFeatures))
    ).sort();
  }, [plans]);

  return (
    <div className="flex-1 flex flex-col items-center justify-center py-12">
      <div className="w-full max-w-5xl px-6">
        <div className="text-center mb-10">
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white">
            Choose Your Plan
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2 max-w-2xl mx-auto">
            {`Select the plan that best fits your team's needs. All plans include
            a ${TRIAL_PERIOD}-${TRIAL_PERIOD_UNIT} free trial.`}
          </p>
        </div>
        {/* Billing Toggle */}
        <div className="w-full flex justify-center">
          <PlanToggle
            selected={billingPeriod}
            onChange={(value) => {
              setBillingPeriod(value);
            }}
          />
        </div>
        {/* Plans List */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-10 mb-6">
          {plans.map((plan, idx) => {
            const isEnterprise = plan.planCode === 'ENTERPRISE';
            const isFirstPlan = idx === 0;
            const isRecommended = plan.planCode === reccommendedPlanCode;

            // Get differential features
            const { uniqueFeatures, previousPlanFeatureCount } =
              getDifferentialFeatures(plan, plans);

            // Limit features to show
            const featurestoDisplay = uniqueFeatures.slice(0, visibleFeatures);
            const remainingFeaturesCount =
              uniqueFeatures.length - featurestoDisplay.length;

            const price = formatPrice(
              billingPeriod === BILLING_PERIOD.MONTHLY
                ? plan.priceCents
                : plan.yearPriceCents / 12,
              plan.currency
            );

            return (
              <div
                key={plan.id}
                className={` flex flex-col relative rounded-xl overflow-hidden ring-2 ring-b6ai-blue dark:ring-b6ai-cyan  bg-white dark:bg-gray-800 shadow-lg transition-all hover:shadow-xl`}
              >
                {isRecommended && (
                  <div className="absolute top-0 right-0 bg-gradient-to-r from-blue-600 to-cyan-500 text-white text-xs font-bold px-3 py-1 rounded-bl-lg">
                    RECOMMENDED
                  </div>
                )}
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="mr-3">
                      <div>{getIcon(idx)}</div>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                      {plan.name}
                    </h3>
                  </div>
                  <div className="mb-4">
                    <span className="text-3xl font-bold text-gray-900 dark:text-white">
                      {price}
                    </span>
                    {plan.planCode !== 'enterprise' && (
                      <span className="text-gray-500 dark:text-gray-400 ml-1">
                        /
                        {billingPeriod === BILLING_PERIOD.MONTHLY
                          ? 'month'
                          : 'month, billed annually'}
                      </span>
                    )}
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    {plan.description}
                  </p>

                  {/* Show "Everything in [Previous Plan]" for non-first plans */}
                  {!isFirstPlan && previousPlanFeatureCount > 0 && (
                    <div className="pb-2 mb-2 border-b">
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        Everything in {plans[idx - 1].name}, plus:
                      </p>
                    </div>
                  )}
                  <ul className="space-y-2 mb-6">
                    {featurestoDisplay.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <CheckIcon
                          size={16}
                          className="text-green-500 mr-2 mt-0.5 flex-shrink-0"
                        />
                        <span className="text-sm text-gray-600 dark:text-gray-300">
                          {feature.feature.name}
                        </span>
                      </li>
                    ))}
                    {!!remainingFeaturesCount && (
                      <li className="text-sm text-gray-500 dark:text-gray-400 pl-6">
                        +{remainingFeaturesCount} more features
                      </li>
                    )}
                  </ul>
                </div>
                <div className="mt-auto p-6 pt-0">
                  <Button
                    onClick={() => handleAction(plan)}
                    disabled={isLoading && selectedPlan?.id === plan.id}
                    className="w-full btn py-3 rounded-lg transition-colors btn-b6ai flex items-center justify-center"
                  >
                    {isLoading && selectedPlan?.id === plan.id ? (
                      <span className="flex items-center">
                        <Loader size={16} className="mr-2" />
                        Loading...
                      </span>
                    ) : !isEnterprise ? (
                      <span className="flex items-center">
                        Start Free Trial
                        <ArrowRightIcon size={16} className="ml-2" />
                      </span>
                    ) : (
                      <span className="flex items-center">
                        Get Call
                        <PhoneIncoming size={16} className="ml-2" />
                      </span>
                    )}
                  </Button>
                </div>
              </div>
            );
          })}
        </div>

        {/* Show Full Feature List Button */}
        <div className="flex justify-center mb-6">
          <button
            onClick={() => setShowFullFeatures(!showFullFeatures)}
            className="flex items-center text-sm text-b6ai-blue dark:text-b6ai-cyan hover:underline transition-colors"
          >
            {showFullFeatures ? (
              <>
                Hide full feature comparison
                <ChevronUpIcon size={16} className="ml-1" />
              </>
            ) : (
              <>
                Show full feature comparison
                <ChevronDownIcon size={16} className="ml-1" />
              </>
            )}
          </button>
        </div>

        {/* Full Feature Comparison Table */}
        {showFullFeatures && (
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden shadow-md mb-8">
            <table className="w-full">
              <thead>
                <tr className="bg-gray-50 dark:bg-gray-700">
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-600 dark:text-gray-300">
                    Features
                  </th>
                  {plans.map((plan) => (
                    <th
                      key={plan.id}
                      className="px-4 py-3 text-left text-sm font-medium text-gray-600 dark:text-gray-300"
                    >
                      {plan.name}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {allFeatures.map((feature, idx) => (
                  <tr
                    key={idx}
                    className={
                      idx % 2 === 0 ? 'bg-gray-50 dark:bg-gray-700/30' : ''
                    }
                  >
                    <td className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 border-t border-gray-200 dark:border-gray-700">
                      {feature.feature.name}
                    </td>
                    {plans.map((plan) => (
                      <td
                        key={`${plan.id}-${idx}`}
                        className="px-4 py-2 text-sm border-t border-gray-200 dark:border-gray-700"
                      >
                        {plan.planFeatures.some(
                          (pf) => feature.featureId === pf.featureId
                        ) ? (
                          <CheckIcon size={16} className="text-green-500" />
                        ) : (
                          <span className="text-gray-400">—</span>
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
