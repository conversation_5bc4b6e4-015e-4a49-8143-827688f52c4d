import React, { useState } from 'react';
import { Input } from 'libs/ui/src';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from 'libs/ui/src/components/ui/dialog';
import {
  I18N_KEYS,
  useLocalization,
} from '../../app/context/localization-context';

interface CreateDepartmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (name: string) => void;
}

export const CreateDepartmentModal: React.FC<CreateDepartmentModalProps> = ({
  isOpen,
  onClose,
  onSave,
}) => {
  const { t } = useLocalization();
  const [departmentName, setDepartmentName] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = () => {
    if (!departmentName.trim()) {
      setError(t(I18N_KEYS.TEAMS.DEPARTMENT_NAME_REQUIRED));
      return;
    }
    onSave(departmentName);
    setDepartmentName('');
    setError('');
    onClose();
  };

  const handleCancel = () => {
    setDepartmentName('');
    setError('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[400px] w-full p-6 rounded-lg">
        <DialogHeader>
          <DialogTitle>{t(I18N_KEYS.TEAMS.CREATE_DEPARTMENT)}</DialogTitle>
        </DialogHeader>

        <div className="mb-4 mt-4">
          <Input
            label={t(I18N_KEYS.TEAMS.DEPARTMENT_NAME)}
            value={departmentName}
            onChange={(e) => {
              setDepartmentName(e.target.value);
              setError('');
            }}
            placeholder={t(I18N_KEYS.TEAMS.ENTER_DEPARTMENT_NAME)}
            error={error}
            fullWidth
          />
        </div>

        <div className="flex justify-end space-x-2">
          <button
            onClick={handleCancel}
            className="px-4 py-2 text-sm rounded bg-light-muted dark:bg-dark-muted text-light-foreground dark:text-dark-foreground hover:bg-light-border dark:hover:bg-dark-border transition-colors"
          >
            {t(I18N_KEYS.COMMON.CANCEL)}
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 text-sm rounded bg-gradient-to-r from-b6ai-cyan via-b6ai-blue to-b6ai-navy text-white transition-colors"
          >
            {t(I18N_KEYS.COMMON.SAVE)}
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
