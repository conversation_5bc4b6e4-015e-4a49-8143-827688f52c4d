import React, { useEffect, useState } from 'react';
import { X } from 'lucide-react';
interface FilterDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  type: string;
  agentFilters: {
    department: string;
    status: string;
    startDate: string;
    endDate: string;
  };
  departmentFilters: {
    status: string;
    startDate: string;
    endDate: string;
  };
  onApplyAgentFilters: (filters: {
    department: string;
    status: string;
    startDate: string;
    endDate: string;
  }) => void;
  onApplyDepartmentFilters: (filters: {
    status: string;
    startDate: string;
    endDate: string;
  }) => void;
}
export const FilterDrawer: React.FC<FilterDrawerProps> = ({
  isOpen,
  onClose,
  type,
  agentFilters,
  departmentFilters,
  onApplyAgentFilters,
  onApplyDepartmentFilters,
}) => {
  const [tempAgentFilters, setTempAgentFilters] = useState(agentFilters);
  const [tempDepartmentFilters, setTempDepartmentFilters] =
    useState(departmentFilters);
  // Reset temp filters when drawer opens
  useEffect(() => {
    if (isOpen) {
      setTempAgentFilters(agentFilters);
      setTempDepartmentFilters(departmentFilters);
    }
  }, [isOpen, agentFilters, departmentFilters]);
  const handleApplyFilters = () => {
    if (type === 'agents') {
      onApplyAgentFilters(tempAgentFilters);
    } else {
      onApplyDepartmentFilters(tempDepartmentFilters);
    }
  };
  const handleClearFilters = () => {
    if (type === 'agents') {
      setTempAgentFilters({
        department: '',
        status: '',
        startDate: '',
        endDate: '',
      });
    } else {
      setTempDepartmentFilters({
        status: '',
        startDate: '',
        endDate: '',
      });
    }
  };
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50">
      <div className="absolute top-0 right-0 w-96 h-full bg-white dark:bg-dark-card shadow-lg transform transition-transform duration-300 ease-in-out">
        <div className="flex justify-between items-center p-4 border-b border-light-border dark:border-dark-border">
          <h2 className="text-lg font-medium text-light-foreground dark:text-dark-foreground">
            Filter {type === 'agents' ? 'Agents' : 'Departments'}
          </h2>
          <button
            className="p-1 rounded hover:bg-light-muted dark:hover:bg-dark-muted transition-colors"
            onClick={onClose}
          >
            <X
              size={20}
              className="text-light-foreground dark:text-dark-foreground"
            />
          </button>
        </div>
        <div className="p-4 space-y-4">
          {type === 'agents' && (
            <div>
              <label className="block text-sm font-medium text-light-foreground dark:text-dark-foreground mb-1">
                Department
              </label>
              <select
                value={tempAgentFilters.department}
                onChange={(e) =>
                  setTempAgentFilters({
                    ...tempAgentFilters,
                    department: e.target.value,
                  })
                }
                className="w-full rounded bg-light-background dark:bg-dark-accent border border-light-border dark:border-dark-border px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
              >
                <option value="">All Departments</option>
                <option value="Customer Support">Customer Support</option>
                <option value="Technical Support">Technical Support</option>
                <option value="Sales Support">Sales Support</option>
                <option value="Billing Support">Billing Support</option>
              </select>
            </div>
          )}
          <div>
            <label className="block text-sm font-medium text-light-foreground dark:text-dark-foreground mb-1">
              Status
            </label>
            <select
              value={
                type === 'agents'
                  ? tempAgentFilters.status
                  : tempDepartmentFilters.status
              }
              onChange={(e) => {
                if (type === 'agents') {
                  setTempAgentFilters({
                    ...tempAgentFilters,
                    status: e.target.value,
                  });
                } else {
                  setTempDepartmentFilters({
                    ...tempDepartmentFilters,
                    status: e.target.value,
                  });
                }
              }}
              className="w-full rounded bg-light-background dark:bg-dark-accent border border-light-border dark:border-dark-border px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              {type === 'agents' && <option value="on leave">On Leave</option>}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-light-foreground dark:text-dark-foreground mb-1">
              Date Range
            </label>
            <div className="flex space-x-2">
              <div className="flex-1">
                <label className="block text-xs text-light-muted-foreground dark:text-dark-muted-foreground mb-1">
                  From
                </label>
                <input
                  type="date"
                  value={
                    type === 'agents'
                      ? tempAgentFilters.startDate
                      : tempDepartmentFilters.startDate
                  }
                  onChange={(e) => {
                    if (type === 'agents') {
                      setTempAgentFilters({
                        ...tempAgentFilters,
                        startDate: e.target.value,
                      });
                    } else {
                      setTempDepartmentFilters({
                        ...tempDepartmentFilters,
                        startDate: e.target.value,
                      });
                    }
                  }}
                  className="w-full rounded bg-light-background dark:bg-dark-accent border border-light-border dark:border-dark-border px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                />
              </div>
              <div className="flex-1">
                <label className="block text-xs text-light-muted-foreground dark:text-dark-muted-foreground mb-1">
                  To
                </label>
                <input
                  type="date"
                  value={
                    type === 'agents'
                      ? tempAgentFilters.endDate
                      : tempDepartmentFilters.endDate
                  }
                  onChange={(e) => {
                    if (type === 'agents') {
                      setTempAgentFilters({
                        ...tempAgentFilters,
                        endDate: e.target.value,
                      });
                    } else {
                      setTempDepartmentFilters({
                        ...tempDepartmentFilters,
                        endDate: e.target.value,
                      });
                    }
                  }}
                  className="w-full rounded bg-light-background dark:bg-dark-accent border border-light-border dark:border-dark-border px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                />
              </div>
            </div>
          </div>
          <div className="pt-4 flex space-x-2">
            <button
              onClick={handleClearFilters}
              className="flex-1 px-4 py-2 text-sm border border-light-border dark:border-dark-border rounded hover:bg-light-muted dark:hover:bg-dark-muted transition-colors"
            >
              Clear Filters
            </button>
            <button
              onClick={handleApplyFilters}
              className="flex-1 px-4 py-2 text-sm bg-b6ai-blue text-white rounded hover:opacity-90 transition-opacity"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
