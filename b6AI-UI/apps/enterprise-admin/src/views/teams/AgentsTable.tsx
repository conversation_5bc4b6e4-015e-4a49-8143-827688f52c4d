import React, { useState, useMemo } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { FilterIcon } from 'lucide-react';
import {
  DataTable,
  createTextColumn,
  createDateColumn,
  createStatusColumn,
} from '@b6ai/ui';
import {
  useGetAgentsQuery,
  useExportAgentsMutation,
  Agent,
  AgentFilters,
} from '@b6ai/shared';
interface AgentsTableProps {
  onFilterClick: () => void;
  filters: {
    department: string;
    status: string;
    startDate: string;
    endDate: string;
  };
}
export const AgentsTable: React.FC<AgentsTableProps> = ({
  onFilterClick,
  filters,
}) => {
  const [searchValue, setSearchValue] = useState('');
  const [page] = useState(1);
  const [pageSize] = useState(10);

  // Convert legacy filters to API format
  const apiFilters: AgentFilters = useMemo(
    () => ({
      department: filters.department || undefined,
      status: filters.status || undefined,
      startDate: filters.startDate || undefined,
      endDate: filters.endDate || undefined,
      search: searchValue || undefined,
    }),
    [filters, searchValue]
  );

  // API calls
  const {
    data: agentsData,
    isLoading,
    refetch,
  } = useGetAgentsQuery({
    page,
    pageSize,
    filters: apiFilters,
    sortBy: 'name',
    sortOrder: 'asc',
  });

  const [exportAgents] = useExportAgentsMutation();

  // Handle export
  const handleExport = async () => {
    try {
      const blob = await exportAgents({
        filters: apiFilters,
      }).unwrap();

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `agents-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  // Define table columns
  const columns: ColumnDef<Agent>[] = useMemo(() => {
    // Status variant helper
    const getStatusVariant = (
      status: string
    ): 'success' | 'error' | 'warning' | 'default' | 'info' | undefined => {
      switch (status) {
        case 'active':
          return 'success';
        case 'inactive':
          return 'error';
        case 'on leave':
          return 'warning';
        default:
          return 'default';
      }
    };

    return [
      createTextColumn('name', 'Name'),
      createTextColumn('email', 'Email'),
      createTextColumn('department', 'Department'),
      createTextColumn('role', 'Role'),
      createStatusColumn('status', 'Status', getStatusVariant),
      createDateColumn('joinedDate', 'Joined Date', 'short'),
    ];
  }, []);
  // Active filters for display
  const activeFilters = useMemo(() => {
    const active = [];

    if (filters.department) {
      active.push({
        label: 'Department',
        value: filters.department,
        onRemove: () => {
          // This would be handled by parent component
          console.log('Remove department filter');
        },
      });
    }

    if (filters.status) {
      active.push({
        label: 'Status',
        value: filters.status,
        onRemove: () => {
          // This would be handled by parent component
          console.log('Remove status filter');
        },
      });
    }

    if (filters.startDate && filters.endDate) {
      active.push({
        label: 'Date Range',
        value: `${filters.startDate} to ${filters.endDate}`,
        onRemove: () => {
          // This would be handled by parent component
          console.log('Remove date range filter');
        },
      });
    }

    return active;
  }, [filters]);

  return (
    <div className="mt-4">
      {/* Filter indicators */}
      {activeFilters.length > 0 && (
        <div className="mb-4 flex flex-wrap gap-2">
          {activeFilters.map((filter, index) => (
            <div
              key={index}
              className="inline-flex items-center gap-2 px-3 py-1 bg-light-muted dark:bg-dark-muted rounded-md text-sm"
            >
              <span className="text-light-muted-foreground dark:text-dark-muted-foreground">
                {filter.label}:
              </span>
              <span className="text-light-foreground dark:text-dark-foreground">
                {filter.value}
              </span>
              <button
                onClick={filter.onRemove}
                className="ml-1 text-light-muted-foreground hover:text-light-foreground dark:text-dark-muted-foreground dark:hover:text-dark-foreground"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Search and Filter Button */}
      <div className="flex justify-between items-center mb-4">
        <div className="relative flex items-center">
          <button
            onClick={onFilterClick}
            className="p-1.5 text-sm rounded border border-light-border dark:border-dark-border hover:bg-light-muted dark:hover:bg-dark-muted transition-colors"
          >
            <FilterIcon
              size={16}
              className="text-light-foreground dark:text-dark-foreground"
            />
          </button>
        </div>
      </div>
      {/* Data Table */}
      <DataTable
        columns={columns}
        data={agentsData?.data || []}
        searchPlaceholder="Search agents..."
        searchValue={searchValue}
        onSearchChange={setSearchValue}
        onRefresh={refetch}
        onExport={handleExport}
        isLoading={isLoading}
        emptyMessage="No agents found."
        pageSize={pageSize}
        enablePagination={true}
        enableSorting={true}
        enableSearch={true}
      />
    </div>
  );
};
