import React, { useState } from 'react';
import { Input } from 'libs/ui/src/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from 'libs/ui/src/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'libs/ui/src/components/ui/select';
import {
  I18N_KEYS,
  useLocalization,
} from '../../app/context/localization-context';

interface CreateAgentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (agent: {
    firstName: string;
    lastName: string;
    email: string;
    department: string;
    phone: string;
  }) => void;
  departments: {
    value: string;
    label: string;
  }[];
}

export const CreateAgentModal: React.FC<CreateAgentModalProps> = ({
  isOpen,
  onClose,
  onSave,
  departments,
}) => {
  const wasOpenRef = React.useRef(isOpen);
  React.useEffect(() => {
    wasOpenRef.current = isOpen;
  }, [isOpen]);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    department: '',
    phone: '',
  });
  const { t } = useLocalization();

  const [errors, setErrors] = useState({
    firstName: '',
    lastName: '',
    email: '',
    department: '',
    phone: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: '' }));
  };

  const handleSubmit = () => {
    let hasErrors = false;
    const newErrors = { ...errors };

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
      hasErrors = true;
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
      hasErrors = true;
    }
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
      hasErrors = true;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Invalid email';
      hasErrors = true;
    }
    if (!formData.department) {
      newErrors.department = 'Department is required';
      hasErrors = true;
    }

    if (hasErrors) {
      setErrors(newErrors);
      return;
    }

    onSave(formData);
    resetForm();
    onClose();
  };

  const resetForm = () => {
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      department: '',
      phone: '',
    });
    setErrors({
      firstName: '',
      lastName: '',
      email: '',
      department: '',
      phone: '',
    });
  };

  const handleCancel = () => {
    resetForm();
    onClose();
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (wasOpenRef.current && !open) {
          resetForm();
          onClose();
        }
      }}
    >
      {' '}
      <DialogContent className="sm:max-w-[500px] w-full p-6 rounded-lg">
        <DialogHeader>
          <DialogTitle>{t(I18N_KEYS.TEAMS.CREATE_AGENT)}</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-2 gap-4 mt-4">
          <Input
            label="First Name"
            name="firstName"
            value={formData.firstName}
            onChange={handleInputChange}
            placeholder="Enter first name"
            error={errors.firstName}
            // fullWidth
          />

          <Input
            label="Last Name"
            name="lastName"
            value={formData.lastName}
            onChange={handleInputChange}
            placeholder="Enter last name"
            error={errors.lastName}
            // fullWidth
          />
          <div className="col-span-2">
            <Input
              label="Email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="Enter email"
              error={errors.email}
              // fullWidth
            />
          </div>
          <div className="col-span-2">
            <Select
              value={formData.department}
              onValueChange={(value) => {
                setFormData((prev) => ({ ...prev, department: value }));
                setErrors((prev) => ({ ...prev, department: '' }));
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select department" />
              </SelectTrigger>
              <SelectContent>
                {departments.map((dept) => (
                  <SelectItem key={dept.value} value={dept.value}>
                    {dept.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.department && (
              <p className="mt-1 text-sm text-light-destructive dark:text-dark-destructive">
                {errors.department}
              </p>
            )}
          </div>
          <div className="col-span-2">
            <Input
              label="Phone"
              name="phone"
              type="tel"
              value={formData.phone}
              onChange={handleInputChange}
              placeholder="Enter phone"
              error={errors.phone}
            />
          </div>
        </div>

        <div className="flex justify-end space-x-2 mt-6">
          <button
            onClick={handleCancel}
            className="px-4 py-2 text-sm rounded bg-light-muted dark:bg-dark-muted text-light-foreground dark:text-dark-foreground hover:bg-light-border dark:hover:bg-dark-border transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 text-sm rounded bg-gradient-to-r from-b6ai-cyan via-b6ai-blue to-b6ai-navy text-white transition-colors"
          >
            Save
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
