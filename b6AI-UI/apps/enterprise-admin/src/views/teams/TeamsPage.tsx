import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>readcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from '@b6ai/ui';
import { DepartmentsTable } from '../teams/DepartmentsTable';
import { AgentsTable } from '../teams/AgentsTable';
import { FilterDrawer } from '../teams/FilterDrawer';
import { CreateDepartmentModal } from '../teams/CreateDepartmentModal';
import { CreateAgentModal } from '../teams/CreateAgentModal';
import { PlusIcon } from 'lucide-react';
import {
  I18N_KEYS,
  useLocalization,
} from '../../app/context/localization-context';
import { useToast } from '../../app/context/toast-context';

export const TeamsPage: React.FC = () => {
  const { t } = useLocalization();
  const toast = useToast();
  const [isFilterDrawerO<PERSON>, setIsFilterDrawerOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('agents');
  const [agentFilters, setAgentFilters] = useState({
    department: '',
    status: '',
    startDate: '',
    endDate: '',
  });
  const [departmentFilters, setDepartmentFilters] = useState({
    status: '',
    startDate: '',
    endDate: '',
  });
  const [isCreateDepartmentModalOpen, setIsCreateDepartmentModalOpen] =
    useState(false);
  const [isCreateAgentModalOpen, setIsCreateAgentModalOpen] = useState(false);
  // Mock department data for dropdown
  const departmentOptions = [
    {
      value: 'Customer Support',
      label: 'Customer Support',
    },
    {
      value: 'Technical Support',
      label: 'Technical Support',
    },
    {
      value: 'Sales Support',
      label: 'Sales Support',
    },
    {
      value: 'Billing Support',
      label: 'Billing Support',
    },
  ];

  const handleOpenFilterDrawer = (tab: string) => {
    setActiveTab(tab);
    setIsFilterDrawerOpen(true);
  };
  const handleApplyAgentFilters = (filters: typeof agentFilters) => {
    setAgentFilters(filters);
    setIsFilterDrawerOpen(false);
  };
  const handleApplyDepartmentFilters = (filters: typeof departmentFilters) => {
    setDepartmentFilters(filters);
    setIsFilterDrawerOpen(false);
  };
  const handleCreateDepartment = (name: string) => {
    // Here you would typically make an API call to create a department
    console.log('Creating department:', name);
    // Then refresh the departments list
    toast.success(t(I18N_KEYS.TOASTS.DEPARTMENT_CREATED));
  };
  const handleCreateAgent = (agent: {
    firstName: string;
    lastName: string;
    email: string;
    department: string;
    phone: string;
  }) => {
    // Here you would typically make an API call to create an agent
    console.log('Creating agent:', agent);
    // Then refresh the agents list
    toast.success(t(I18N_KEYS.TOASTS.AGENT_CREATED));
  };
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };
  const tabItems = [
    {
      id: 'agents',
      label: t(I18N_KEYS.TEAMS.AGENTS),
      content: (
        <div>
          <div className="flex justify-end mt-6 mb-4">
            <button
              onClick={() => setIsCreateAgentModalOpen(true)}
              className="flex items-center px-4 py-2 text-sm rounded bg-gradient-to-r from-b6ai-cyan via-b6ai-blue to-b6ai-navy text-white transition-colors"
            >
              <PlusIcon size={16} className="mr-1" />
              {t(I18N_KEYS.TEAMS.CREATE_AGENT)}
            </button>
          </div>
          <AgentsTable
            onFilterClick={() => handleOpenFilterDrawer('agents')}
            filters={agentFilters}
          />
        </div>
      ),
    },
    {
      id: 'departments',
      label: t(I18N_KEYS.TEAMS.DEPARTMENTS),
      content: (
        <div>
          <div className="flex justify-end mt-6 mb-4">
            <button
              onClick={() => setIsCreateDepartmentModalOpen(true)}
              className="flex items-center px-4 py-2 text-sm rounded bg-gradient-to-r from-b6ai-cyan via-b6ai-blue to-b6ai-navy text-white transition-colors"
            >
              <PlusIcon size={16} className="mr-1" />
              {t(I18N_KEYS.TEAMS.CREATE_DEPARTMENT)}
            </button>
          </div>
          <DepartmentsTable
            onFilterClick={() => handleOpenFilterDrawer('departments')}
            filters={departmentFilters}
          />
        </div>
      ),
    },
  ];
  return (
    <div className="p-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">
              {t(I18N_KEYS.NAV.DASHBOARD)}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{t(I18N_KEYS.TEAMS.TITLE)}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <h1 className="text-2xl font-semibold text-light-foreground dark:text-dark-foreground mb-6">
        {t(I18N_KEYS.TEAMS.TITLE)}
      </h1>
      <Tabs
        items={tabItems}
        defaultTab="agents"
        onTabChange={handleTabChange}
      />

      {/* Filter Drawer */}
      <FilterDrawer
        isOpen={isFilterDrawerOpen}
        onClose={() => setIsFilterDrawerOpen(false)}
        type={activeTab}
        agentFilters={agentFilters}
        departmentFilters={departmentFilters}
        onApplyAgentFilters={handleApplyAgentFilters}
        onApplyDepartmentFilters={handleApplyDepartmentFilters}
      />
      {/* Create Department Modal */}
      <CreateDepartmentModal
        isOpen={isCreateDepartmentModalOpen}
        onClose={() => setIsCreateDepartmentModalOpen(false)}
        onSave={handleCreateDepartment}
      />
      {/* Create Agent Modal */}
      <CreateAgentModal
        isOpen={isCreateAgentModalOpen}
        onClose={() => setIsCreateAgentModalOpen(false)}
        onSave={handleCreateAgent}
        departments={departmentOptions}
      />
    </div>
  );
};
