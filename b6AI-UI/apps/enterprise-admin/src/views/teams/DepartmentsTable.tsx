import React, { useState } from 'react';
import { SearchIcon, FilterIcon, ArrowUpDownIcon } from 'lucide-react';
interface Department {
  id: string;
  name: string;
  manager: string;
  agents: number;
  status: 'active' | 'inactive';
  createdDate: string;
}
interface DepartmentsTableProps {
  onFilterClick: () => void;
  filters: {
    status: string;
    startDate: string;
    endDate: string;
  };
}
export const DepartmentsTable: React.FC<DepartmentsTableProps> = ({
  onFilterClick,
  filters,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof Department>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  // Mock data for departments
  const departments: Department[] = [
    {
      id: '1',
      name: 'Customer Support',
      manager: '<PERSON>',
      agents: 12,
      status: 'active',
      createdDate: '2020-05-10',
    },
    {
      id: '2',
      name: 'Technical Support',
      manager: '<PERSON>',
      agents: 8,
      status: 'active',
      createdDate: '2021-02-15',
    },
    {
      id: '3',
      name: 'Sales Support',
      manager: '<PERSON>',
      agents: 5,
      status: 'active',
      createdDate: '2022-01-20',
    },
    {
      id: '4',
      name: 'Billing Support',
      manager: 'Henry Wilson',
      agents: 4,
      status: 'inactive',
      createdDate: '2021-09-12',
    },
  ];
  const handleSort = (field: keyof Department) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  // Filter the departments based on search term and filters
  let filteredDepartments = departments.filter(
    (department) =>
      department.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      department.manager.toLowerCase().includes(searchTerm.toLowerCase())
  );
  // Apply filters
  if (filters.status) {
    filteredDepartments = filteredDepartments.filter(
      (department) => department.status === filters.status
    );
  }
  if (filters.startDate && filters.endDate) {
    filteredDepartments = filteredDepartments.filter(
      (department) =>
        new Date(department.createdDate) >= new Date(filters.startDate) &&
        new Date(department.createdDate) <= new Date(filters.endDate)
    );
  }
  // Sort the filtered departments
  const sortedDepartments = [...filteredDepartments].sort((a, b) => {
    if (a[sortField] < b[sortField]) return sortDirection === 'asc' ? -1 : 1;
    if (a[sortField] > b[sortField]) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });
  const getStatusColor = (status: Department['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };
  // Format date to be more readable
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };
  return (
    <div className="mt-4">
      <div className="flex justify-between items-center mb-4">
        <div className="relative flex items-center">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search departments..."
            className="pl-8 pr-3 py-1.5 text-sm rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-accent focus:outline-none focus:ring-1 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan w-64"
          />
          <SearchIcon
            size={16}
            className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-light-muted-foreground dark:text-dark-muted-foreground"
          />
          <button
            onClick={onFilterClick}
            className="ml-2 p-1.5 text-sm rounded border border-light-border dark:border-dark-border hover:bg-light-muted dark:hover:bg-dark-muted transition-colors"
          >
            <FilterIcon
              size={16}
              className="text-light-foreground dark:text-dark-foreground"
            />
          </button>
        </div>
        {/* Filter indicators */}
        {(filters.status || (filters.startDate && filters.endDate)) && (
          <div className="flex items-center space-x-2 text-sm text-light-muted-foreground dark:text-dark-muted-foreground">
            <span>Filtered by:</span>
            {filters.status && (
              <span className="px-2 py-1 rounded-full bg-light-muted dark:bg-dark-muted">
                Status: {filters.status}
              </span>
            )}
            {filters.startDate && filters.endDate && (
              <span className="px-2 py-1 rounded-full bg-light-muted dark:bg-dark-muted">
                Date: {filters.startDate} to {filters.endDate}
              </span>
            )}
          </div>
        )}
      </div>
      <div className="overflow-x-auto">
        <table className="w-full text-sm text-left">
          <thead className="text-xs text-light-muted-foreground dark:text-dark-muted-foreground uppercase bg-light-muted dark:bg-dark-muted">
            <tr>
              <th
                className="px-4 py-3 cursor-pointer"
                onClick={() => handleSort('name')}
              >
                <div className="flex items-center">
                  Department Name
                  {sortField === 'name' && (
                    <ArrowUpDownIcon size={14} className="ml-1" />
                  )}
                </div>
              </th>
              <th
                className="px-4 py-3 cursor-pointer"
                onClick={() => handleSort('manager')}
              >
                <div className="flex items-center">
                  Manager
                  {sortField === 'manager' && (
                    <ArrowUpDownIcon size={14} className="ml-1" />
                  )}
                </div>
              </th>
              <th
                className="px-4 py-3 cursor-pointer"
                onClick={() => handleSort('agents')}
              >
                <div className="flex items-center">
                  Agents
                  {sortField === 'agents' && (
                    <ArrowUpDownIcon size={14} className="ml-1" />
                  )}
                </div>
              </th>
              <th
                className="px-4 py-3 cursor-pointer"
                onClick={() => handleSort('status')}
              >
                <div className="flex items-center">
                  Status
                  {sortField === 'status' && (
                    <ArrowUpDownIcon size={14} className="ml-1" />
                  )}
                </div>
              </th>
              <th
                className="px-4 py-3 cursor-pointer"
                onClick={() => handleSort('createdDate')}
              >
                <div className="flex items-center">
                  Created Date
                  {sortField === 'createdDate' && (
                    <ArrowUpDownIcon size={14} className="ml-1" />
                  )}
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            {sortedDepartments.length === 0 ? (
              <tr>
                <td
                  colSpan={5}
                  className="px-4 py-6 text-center text-light-muted-foreground dark:text-dark-muted-foreground"
                >
                  No departments found
                </td>
              </tr>
            ) : (
              sortedDepartments.map((department) => (
                <tr
                  key={department.id}
                  className="border-b border-light-border dark:border-dark-border hover:bg-light-muted dark:hover:bg-dark-muted cursor-pointer"
                >
                  <td className="px-4 py-3 font-medium">{department.name}</td>
                  <td className="px-4 py-3">{department.manager}</td>
                  <td className="px-4 py-3">{department.agents}</td>
                  <td className="px-4 py-3">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                        department.status
                      )}`}
                    >
                      {department.status.charAt(0).toUpperCase() +
                        department.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-4 py-3">
                    {formatDate(department.createdDate)}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};
