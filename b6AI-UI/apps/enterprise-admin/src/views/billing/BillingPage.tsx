import React, { useMemo, useState } from 'react';
import {
  ArrowLeftIcon,
  SaveIcon,
  CreditCardIcon,
  CheckCircleIcon,
  ArrowUpIcon,
  CalendarIcon,
  DownloadIcon,
  RefreshCwIcon,
  BadgeAlertIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  Home,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import {
  Plan,
  BILLING_PERIOD,
  CURRENCY_SYMBOLS,
  ISubscription,
  I18N_KEYS,
  PlanFeature,
} from '@b6ai/shared';
import { PlanToggle } from '../subscription/PlanToggle';
import { useLocalization } from '../../app/context/localization-context';
import {
  Button,
  Card,
  CardContent,
  CardHeader,
  Checkbox,
  Input,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Textarea,
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@b6ai/ui';

interface BillingPageProps {
  plans: Plan[];
  billingPeriod: BILLING_PERIOD;
  setBillingPeriod: (period: BILLING_PERIOD) => void;
  onGetStarted: (plan: Plan) => void;
  isLoading: boolean;
  selectedPlan: Plan | null;
  subscription: ISubscription | null;
  canceling: boolean;
  handleCancel: () => void;
}

export const BillingPage: React.FC<BillingPageProps> = ({
  plans,
  billingPeriod,
  setBillingPeriod,
  onGetStarted,
  isLoading,
  selectedPlan,
  canceling,
  handleCancel,
}) => {
  const navigate = useRouter();
  const { t } = useLocalization();
  const [showFullFeatures, setShowFullFeatures] = useState(false);
  const [sendReceipt, setSendReceipt] = useState(true);

  const allFeatures: PlanFeature[] = useMemo(() => {
    return Array.from(
      new Set(plans.flatMap((plan) => plan.planFeatures))
    ).sort();
  }, [plans]);

  const formatPrice = (priceCents: number, currency: string) => {
    const symbol = CURRENCY_SYMBOLS[currency] || currency;
    const price = (priceCents / 100).toFixed(2);
    // Remove .00 for cleaner look
    return `${symbol}${price.replace(/\.00$/, '')}`;
  };

  const handlePlanSwitch = (plan: Plan) => {
    onGetStarted(plan);
  };

  // Sample billing history
  const billingHistory = [
    {
      id: 'INV-001234',
      date: '2023-10-01',
      amount: '$299.00',
      status: 'Paid',
      description: 'TenantFlow Enterprise Plan - October 2023',
    },
    {
      id: 'INV-001189',
      date: '2023-09-01',
      amount: '$299.00',
      status: 'Paid',
      description: 'TenantFlow Enterprise Plan - September 2023',
    },
    {
      id: 'INV-001132',
      date: '2023-08-01',
      amount: '$299.00',
      status: 'Paid',
      description: 'TenantFlow Enterprise Plan - August 2023',
    },
    {
      id: 'INV-001087',
      date: '2023-07-01',
      amount: '$249.00',
      status: 'Paid',
      description: 'TenantFlow Professional Plan - July 2023',
    },
  ];
  return (
    <div className="w-full">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink
              href="/dashboard"
              className="flex items-center gap-1"
            >
              <Home size={16} /> {/* Home icon */}
              {t(I18N_KEYS.SETTINGS.HOME)} {/* Localized Home */}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/settings">
              {t(I18N_KEYS.SETTINGS.TITLE)} {/* Localized Settings */}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>
              {t(I18N_KEYS.SETTINGS.BILLING_AND_SUBSCRIPTION)}{' '}
              {/* Localized Billing & Subscription */}
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex justify-between items-center mb-6 mt-4">
        <div className="flex items-center">
          <button
            onClick={() => navigate.push('/settings')}
            className="mr-4 p-1 rounded hover:bg-light-muted dark:hover:bg-dark-muted transition-colors"
          >
            <ArrowLeftIcon size={20} />
          </button>
          <div>
            <h1 className="text-2xl font-semibold text-light-foreground dark:text-dark-foreground">
              {t(I18N_KEYS.BILLING.TITLE)}
            </h1>
            <p className="text-sm text-light-muted-foreground dark:text-dark-muted-foreground">
              {t(I18N_KEYS.BILLING.DESCRIPTION)}
            </p>
          </div>
        </div>
        <Button className="flex items-center px-4 py-2 btn-b6ai rounded-md">
          <SaveIcon size={18} className="mr-2" />
          {t(I18N_KEYS.SETTINGS.SAVE_CHANGES)}
        </Button>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Current Plan */}
        <Card className="bg-light-card dark:bg-dark-card border-light-border dark:border-dark-border overflow-hidden">
          <CardHeader className="border-b border-light-border dark:border-dark-border p-4">
            <div className="flex items-center">
              <CreditCardIcon
                size={20}
                className="mr-2 text-b6ai-blue dark:text-b6ai-cyan"
              />
              <h2 className="text-lg font-medium">
                {t(I18N_KEYS.BILLING.CURRENT_PLAN)}
              </h2>
            </div>
          </CardHeader>

          <CardContent className="p-6 space-y-6">
            {/* Plan Info */}
            <div className="bg-light-muted dark:bg-dark-muted p-3 rounded-lg space-y-4">
              {/* Plan Info */}
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="font-medium">
                    {t(I18N_KEYS.BILLING.ENTERPRISE_PLAN)}
                  </span>
                  <span className="text-sm bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full">
                    {t(I18N_KEYS.BILLING.ACTIVE)}
                  </span>
                </div>
                <div className="text-sm text-light-muted-foreground dark:text-dark-muted-foreground">
                  Renews on October 15, 2023
                </div>
              </div>

              {/* Payment Method */}
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm font-medium">
                    {t(I18N_KEYS.BILLING.PAYMENT_METHOD)}
                  </span>
                  <button className="text-xs text-b6ai-blue dark:text-b6ai-cyan">
                    {t(I18N_KEYS.BILLING.UPDATE)}
                  </button>
                </div>
                <div className="flex items-center">
                  <div className="w-10 h-6 bg-light-muted dark:bg-dark-muted rounded flex items-center justify-center mr-2">
                    <span className="text-xs font-bold">VISA</span>
                  </div>
                  <span className="text-sm">•••• 4242</span>
                </div>
              </div>

              {/* Plan Limits */}
              <div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm font-medium">
                    {t(I18N_KEYS.BILLING.PLAN_LIMITS)}
                  </span>
                </div>
                <div className="space-y-3">
                  {/* Agents */}
                  <div>
                    <div className="flex justify-between mb-1 text-xs">
                      <span>{t(I18N_KEYS.BILLING.AGENTS)}</span>
                      <span>45 / {t(I18N_KEYS.BILLING.UNLIMITED)}</span>
                    </div>
                    <div className="w-full bg-light-muted dark:bg-dark-muted rounded-full h-2 overflow-hidden">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{ width: '45%' }}
                      ></div>
                    </div>
                  </div>
                  {/* Monthly Messages */}
                  <div>
                    <div className="flex justify-between mb-1 text-xs">
                      <span>{t(I18N_KEYS.BILLING.MONTHLY_MESSAGES)}</span>
                      <span>85K / 100K</span>
                    </div>
                    <div className="w-full bg-light-muted dark:bg-dark-muted rounded-full h-2 overflow-hidden">
                      <div
                        className="bg-yellow-500 h-2 rounded-full"
                        style={{ width: '85%' }}
                      ></div>
                    </div>
                  </div>
                  {/* Storage */}
                  <div>
                    <div className="flex justify-between mb-1 text-xs">
                      <span>{t(I18N_KEYS.BILLING.STORAGE)}</span>
                      <span>52GB / 100GB</span>
                    </div>
                    <div className="w-full bg-light-muted dark:bg-dark-muted rounded-full h-2 overflow-hidden">
                      <div
                        className="bg-b6ai-blue dark:bg-b6ai-cyan h-2 rounded-full"
                        style={{ width: '52%' }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <Button className="w-full px-4 py-2 btn-b6ai rounded-md">
              {t(I18N_KEYS.BILLING.CHANGE_PLAN)}
            </Button>
          </CardContent>
        </Card>
        {/* Billing Information */}
        <Card className="bg-light-card dark:bg-dark-card border-light-border dark:border-dark-border overflow-hidden">
          <CardHeader className="border-b border-light-border dark:border-dark-border p-4">
            <div className="flex items-center">
              <CreditCardIcon
                size={20}
                className="mr-2 text-b6ai-blue dark:text-b6ai-cyan"
              />
              <h2 className="text-lg font-medium">
                {t(I18N_KEYS.BILLING.BILLING_INFORMATION)}
              </h2>
            </div>
          </CardHeader>

          <CardContent className="p-6 space-y-6">
            {/* Company Name */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {t(I18N_KEYS.BILLING.COMPANY_NAME)}
              </label>
              <Input
                type="text"
                placeholder="Enter your company name"
                className="w-full px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
              />
            </div>

            {/* Billing Email */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {t(I18N_KEYS.BILLING.BILLING_EMAIL)}
              </label>
              <Input
                type="email"
                placeholder="Enter your email"
                className="w-full px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
              />
            </div>

            {/* Billing Address */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {t(I18N_KEYS.BILLING.BILLING_ADDRESS)}
              </label>
              <Textarea
                placeholder="123 Tech Boulevard, Suite 456, San Francisco, CA 94107, United States"
                rows={3}
                className="w-full px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
              />
            </div>

            {/* Tax ID / VAT */}
            <div>
              <label className="block text-sm font-medium mb-1">
                {t(I18N_KEYS.BILLING.TAX_ID)}
              </label>
              <Input
                type="text"
                placeholder="Eg: US123456789"
                className="w-full px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
              />
            </div>

            {/* Email receipts checkbox */}
            <div className="flex items-center">
              <Checkbox
                id="send-receipt"
                checked={sendReceipt}
                onCheckedChange={(checked: boolean | 'indeterminate') =>
                  setSendReceipt(!!checked)
                }
                className="w-4 h-4 rounded border-light-border dark:border-dark-border focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
              />

              <label
                htmlFor="send-receipt"
                className="ml-2 text-sm cursor-pointer"
              >
                {t(I18N_KEYS.BILLING.EMAIL_RECEIPTS)}
              </label>
            </div>

            <Button className="w-full px-4 py-2 btn-b6ai rounded-md">
              {t(I18N_KEYS.BILLING.SAVE_BILLING_INFO)}
            </Button>
          </CardContent>
        </Card>

        {/* Payment Methods */}
        <Card className="bg-light-card dark:bg-dark-card border-light-border dark:border-dark-border overflow-hidden">
          <CardHeader className="border-b border-light-border dark:border-dark-border p-4">
            <div className="flex items-center">
              <CreditCardIcon
                size={20}
                className="mr-2 text-b6ai-blue dark:text-b6ai-cyan"
              />
              <h2 className="text-lg font-medium">
                {t(I18N_KEYS.BILLING.PAYMENT_METHODS)}
              </h2>
            </div>
          </CardHeader>

          <CardContent className="p-6 space-y-4">
            {/* Payment Method 1 */}
            <Card className="relative bg-light-muted dark:bg-dark-muted rounded-lg p-4">
              {/* Default Badge */}
              <div className="absolute top-3 right-3 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs px-2 py-1 rounded-full">
                {t(I18N_KEYS.BILLING.DEFAULT)}
              </div>

              {/* Card Content */}
              <div className="flex items-center mb-2">
                <div className="w-10 h-6 bg-white rounded flex items-center justify-center mr-3">
                  <span className="text-xs font-bold">VISA</span>
                </div>
                <div>
                  <p className="text-sm font-medium">Visa ending in 4242</p>
                  <p className="text-xs text-light-muted-foreground dark:text-dark-muted-foreground">
                    {t(I18N_KEYS.BILLING.EXPIRES)} 05/2025
                  </p>
                </div>
              </div>

              {/* Actions */}
              <div className="mt-3 flex space-x-2 text-xs">
                <span
                  className="text-b6ai-blue dark:text-b6ai-cyan cursor-pointer hover:underline"
                  onClick={() => console.log('Edit clicked')}
                >
                  {t(I18N_KEYS.BILLING.EDIT)}
                </span>
                <span className="text-light-muted-foreground dark:text-dark-muted-foreground">
                  •
                </span>
                <span
                  className="text-b6ai-blue dark:text-b6ai-cyan cursor-pointer hover:underline"
                  onClick={() => console.log('Remove clicked')}
                >
                  {t(I18N_KEYS.BILLING.REMOVE)}
                </span>
              </div>
            </Card>

            {/* Payment Method 2 */}
            <Card className="bg-light-muted dark:bg-dark-muted rounded-lg p-4">
              {/* Card Content */}
              <div className="flex items-center mb-2">
                <div className="w-10 h-6 bg-white rounded flex items-center justify-center mr-3">
                  <span className="text-xs font-bold">MC</span>
                </div>
                <div>
                  <p className="text-sm font-medium">
                    Mastercard ending in 8888
                  </p>
                  <p className="text-xs text-light-muted-foreground dark:text-dark-muted-foreground">
                    {t(I18N_KEYS.BILLING.EXPIRES)} 11/2024
                  </p>
                </div>
              </div>

              {/* Actions */}
              <div className="mt-3 flex space-x-2 text-xs">
                <span
                  className="text-b6ai-blue dark:text-b6ai-cyan cursor-pointer hover:underline"
                  onClick={() => console.log('Edit clicked')}
                >
                  {t(I18N_KEYS.BILLING.EDIT)}
                </span>
                <span className="text-light-muted-foreground dark:text-dark-muted-foreground">
                  •
                </span>
                <span
                  className="text-b6ai-blue dark:text-b6ai-cyan cursor-pointer hover:underline"
                  onClick={() => console.log('Set as default clicked')}
                >
                  {t(I18N_KEYS.BILLING.SET_AS_DEFAULT)}
                </span>
                <span className="text-light-muted-foreground dark:text-dark-muted-foreground">
                  •
                </span>
                <span
                  className="text-b6ai-blue dark:text-b6ai-cyan cursor-pointer hover:underline"
                  onClick={() => console.log('Remove clicked')}
                >
                  {t(I18N_KEYS.BILLING.REMOVE)}
                </span>
              </div>
            </Card>

            {/* Add Payment Method Button */}
            <button className="w-full py-2 text-sm text-b6ai-blue dark:text-b6ai-cyan border border-b6ai-blue dark:border-b6ai-cyan rounded">
              {t(I18N_KEYS.BILLING.ADD_PAYMENT_METHOD)}
            </button>
          </CardContent>
        </Card>

        {/* Billing History */}
        <Card className="lg:col-span-3 bg-light-card dark:bg-dark-card border-light-border dark:border-dark-border overflow-hidden">
          <CardHeader className="border-b border-light-border dark:border-dark-border p-4">
            <div className="flex items-center">
              <CalendarIcon
                size={20}
                className="mr-2 text-b6ai-blue dark:text-b6ai-cyan"
              />
              <h2 className="text-lg font-medium">
                {t(I18N_KEYS.BILLING.BILLING_HISTORY)}
              </h2>
            </div>
          </CardHeader>

          <CardContent className="p-6">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t(I18N_KEYS.BILLING.INVOICE)}</TableHead>
                    <TableHead>{t(I18N_KEYS.BILLING.DATE)}</TableHead>
                    <TableHead>
                      {t(I18N_KEYS.BILLING.INVOICE_DESCRIPTION)}
                    </TableHead>
                    <TableHead className="text-right">
                      {t(I18N_KEYS.BILLING.AMOUNT)}
                    </TableHead>
                    <TableHead>{t(I18N_KEYS.BILLING.STATUS)}</TableHead>
                    <TableHead className="text-right">
                      {t(I18N_KEYS.BILLING.ACTION)}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {billingHistory.map((invoice) => (
                    <TableRow key={invoice.id}>
                      <TableCell>{invoice.id}</TableCell>
                      <TableCell>{invoice.date}</TableCell>
                      <TableCell>{invoice.description}</TableCell>
                      <TableCell className="text-right">
                        {invoice.amount}
                      </TableCell>
                      <TableCell>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                          <CheckCircleIcon size={12} className="mr-1" />
                          {invoice.status}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <button className="text-b6ai-blue dark:text-b6ai-cyan hover:text-b6ai-navy dark:hover:text-b6ai-blue inline-flex items-center">
                          <DownloadIcon size={14} className="mr-1" />
                          {t(I18N_KEYS.BILLING.PDF)}
                        </button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Available Plans */}
        <Card className="lg:col-span-3 bg-light-card dark:bg-dark-card border-light-border dark:border-dark-border overflow-hidden">
          <CardHeader className="border-b border-light-border dark:border-dark-border p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <ArrowUpIcon
                  size={20}
                  className="mr-2 text-b6ai-blue dark:text-b6ai-cyan"
                />
                <h2 className="text-lg font-medium">
                  {t(I18N_KEYS.BILLING.AVAILABLE_PLANS)}
                </h2>
              </div>
              <PlanToggle
                selected={billingPeriod}
                onChange={setBillingPeriod}
              />
            </div>
          </CardHeader>

          <CardContent className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {plans.map((plan, idx) => {
                const isCurrentPlan = plan.planCode === 'ENTERPRISE';
                const price = formatPrice(
                  billingPeriod === BILLING_PERIOD.MONTHLY
                    ? plan.priceCents
                    : plan.yearPriceCents / 12,
                  plan.currency
                );

                return (
                  <Card
                    key={plan.id}
                    className={`p-6 border flex flex-col ${
                      isCurrentPlan
                        ? 'border-b6ai-blue dark:border-b6ai-cyan bg-blue-50 dark:bg-blue-900/20'
                        : 'border-light-border dark:border-dark-border'
                    }`}
                  >
                    <div className="flex-1">
                      <div className="flex items-center mb-4">
                        <h3 className="text-lg font-bold">{plan.name}</h3>
                      </div>
                      <div className="text-2xl font-bold mb-4">
                        {price}
                        <span className="text-sm font-normal text-light-muted-foreground dark:text-dark-muted-foreground">
                          /
                          {billingPeriod === BILLING_PERIOD.MONTHLY
                            ? t(I18N_KEYS.BILLING.MONTH)
                            : t(I18N_KEYS.BILLING.MONTH_BILLED_ANNUALLY)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                        {plan.description}
                      </p>

                      <ul className="mb-6 space-y-2 max-h-48 overflow-y-auto">
                        {plan.planFeatures
                          .slice(0, 5)
                          .filter((pf) => pf.feature)
                          .map((planFeature, index) => (
                            <li key={index} className="flex items-start">
                              <CheckCircleIcon
                                size={16}
                                className="text-green-500 mr-2 mt-0.5"
                              />
                              <span className="text-sm">
                                {planFeature.feature.name}
                              </span>
                            </li>
                          ))}
                        {plan.planFeatures.length > 5 && (
                          <li className="text-sm text-gray-500 dark:text-gray-400 pl-6">
                            +{plan.planFeatures.length - 5}{' '}
                            {t(I18N_KEYS.BILLING.MORE_FEATURES)}
                          </li>
                        )}
                      </ul>
                    </div>

                    {/* Fixed Bottom Button */}
                    {isCurrentPlan ? (
                      <Button className="flex items-center px-4 py-2 btn-b6ai rounded-md mt-auto">
                        {t(I18N_KEYS.BILLING.CURRENT_PLAN_BADGE)}
                      </Button>
                    ) : (
                      <Button
                        onClick={() => handlePlanSwitch(plan)}
                        disabled={isLoading && selectedPlan?.id === plan.id}
                        className="flex items-center px-4 py-2 btn-b6ai rounded-md mt-auto"
                      >
                        {isLoading && selectedPlan?.id === plan.id
                          ? t(I18N_KEYS.BILLING.SWITCHING)
                          : t(I18N_KEYS.BILLING.SWITCH_PLAN)}
                      </Button>
                    )}
                  </Card>
                );
              })}
            </div>

            {/* Show Full Feature Comparison Button */}
            <div className="flex justify-center mt-6">
              <Button
                variant="link"
                onClick={() => setShowFullFeatures(!showFullFeatures)}
                className="flex items-center text-sm text-b6ai-blue dark:text-b6ai-cyan hover:underline"
              >
                {showFullFeatures ? (
                  <>
                    {t(I18N_KEYS.BILLING.HIDE_FULL_COMPARISON)}
                    <ChevronUpIcon size={16} className="ml-1" />
                  </>
                ) : (
                  <>
                    {t(I18N_KEYS.BILLING.SHOW_FULL_COMPARISON)}
                    <ChevronDownIcon size={16} className="ml-1" />
                  </>
                )}
              </Button>
            </div>

            {/* Full Feature Comparison Table */}
            {showFullFeatures && (
              <Card className="mt-6 overflow-x-auto">
                <CardContent className="p-0">
                  <Table className="w-full">
                    <TableHeader>
                      <TableRow>
                        <TableHead>{t(I18N_KEYS.BILLING.FEATURES)}</TableHead>
                        {plans.map((plan) => (
                          <TableHead key={plan.id}>{plan.name}</TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {allFeatures.map((feature, idx) => (
                        <tr
                          key={idx}
                          className={
                            idx % 2 === 0
                              ? 'bg-gray-50 dark:bg-gray-700/30'
                              : ''
                          }
                        >
                          <td className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 border-t border-gray-200 dark:border-gray-700">
                            {feature.feature.name}
                          </td>
                          {plans.map((plan) => (
                            <td
                              key={`${plan.id}-${idx}`}
                              className="px-4 py-2 text-sm border-t border-gray-200 dark:border-gray-700"
                            >
                              {plan.planFeatures.some(
                                (pf) => feature.featureId === pf.featureId
                              ) ? (
                                <CheckCircleIcon
                                  size={16}
                                  className="text-green-500"
                                />
                              ) : (
                                <span className="text-gray-400">—</span>
                              )}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            )}
          </CardContent>
        </Card>

        {/* Additional Services */}
        <Card className="lg:col-span-3 bg-light-card dark:bg-dark-card border-light-border dark:border-dark-border overflow-hidden">
          <CardHeader className="border-b border-light-border dark:border-dark-border p-4">
            <div className="flex items-center">
              <RefreshCwIcon
                size={20}
                className="mr-2 text-b6ai-blue dark:text-b6ai-cyan"
              />
              <h2 className="text-lg font-medium">
                {t(I18N_KEYS.BILLING.ADDONS_USAGE)}
              </h2>
            </div>
          </CardHeader>

          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Additional Storage */}
              <Card className="p-4 border border-light-border dark:border-dark-border">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-medium">
                      {t(I18N_KEYS.BILLING.ADDITIONAL_STORAGE)}
                    </h3>
                    <p className="text-xs text-light-muted-foreground dark:text-dark-muted-foreground">
                      $10 per 10GB / month
                    </p>
                  </div>
                  <span className="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full">
                    Active
                  </span>
                </div>
                <div className="flex justify-between items-center text-sm mb-2">
                  <span>{t(I18N_KEYS.BILLING.CURRENT_USAGE)}</span>
                  <span>+20GB</span>
                </div>
                <div className="flex justify-between items-center text-sm mb-4">
                  <span>{t(I18N_KEYS.BILLING.MONTHLY_COST)}</span>
                  <span>$20.00</span>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" className="flex-1 py-1.5">
                    {t(I18N_KEYS.BILLING.ADJUST)}
                  </Button>
                  <Button variant="destructive" className="flex-1 py-1.5 ">
                    {t(I18N_KEYS.BILLING.REMOVE)}
                  </Button>
                </div>
              </Card>

              {/* Additional Messages */}
              <Card className="p-4 border border-light-border dark:border-dark-border">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-medium">
                      {t(I18N_KEYS.BILLING.ADDITIONAL_MESSAGES)}
                    </h3>
                    <p className="text-xs text-light-muted-foreground dark:text-dark-muted-foreground">
                      $20 per 10K messages / month
                    </p>
                  </div>
                  <span className="text-xs bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-2 py-1 rounded-full">
                    {t(I18N_KEYS.BILLING.NOT_ACTIVE)}
                  </span>
                </div>
                <div className="flex justify-between items-center text-sm mb-2">
                  <span>{t(I18N_KEYS.BILLING.CURRENT_USAGE)}</span>
                  <span>0 additional</span>
                </div>
                <div className="flex justify-between items-center text-sm mb-4">
                  <span>{t(I18N_KEYS.BILLING.MONTHLY_COST)}</span>
                  <span>$0.00</span>
                </div>
                <Button variant="outline" className="w-full py-1.5 ">
                  {t(I18N_KEYS.BILLING.ADD_MESSAGES)}
                </Button>
              </Card>

              {/* Premium Support */}
              <Card className="p-4 border border-light-border dark:border-dark-border">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-medium">
                      {t(I18N_KEYS.BILLING.PREMIUM_SUPPORT)}
                    </h3>
                    <p className="text-xs text-light-muted-foreground dark:text-dark-muted-foreground">
                      $99 / month
                    </p>
                  </div>
                  <span className="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full">
                    Active
                  </span>
                </div>
                <div className="flex justify-between items-center text-sm mb-2">
                  <span>{t(I18N_KEYS.BILLING.SUPPORT_LEVEL)}</span>
                  <span>{t(I18N_KEYS.BILLING.PRIORITY)}</span>
                </div>
                <div className="flex justify-between items-center text-sm mb-4">
                  <span>{t(I18N_KEYS.BILLING.MONTHLY_COST)}</span>
                  <span>$99.00</span>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" className="flex-1 py-1.5">
                    {t(I18N_KEYS.BILLING.DETAILS)}
                  </Button>
                  <Button variant="destructive" className="flex-1 py-1.5">
                    Cancel
                  </Button>
                </div>
              </Card>
            </div>
          </CardContent>
        </Card>

        {/* Cancellation */}
        <Card className="lg:col-span-3 bg-light-card dark:bg-dark-card border-light-border dark:border-dark-border overflow-hidden">
          <CardHeader className="border-b border-light-border dark:border-dark-border p-4">
            <div className="flex items-center">
              <BadgeAlertIcon size={20} className="mr-2 text-red-500" />
              <h2 className="text-lg font-medium">
                {t(I18N_KEYS.BILLING.CANCEL_SUBSCRIPTION)}
              </h2>
            </div>
          </CardHeader>

          <CardContent className="p-6 space-y-4">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex items-start">
                <BadgeAlertIcon
                  size={18}
                  className="text-red-500 mt-0.5 mr-3 flex-shrink-0"
                />
                <div>
                  <p className="text-sm font-medium text-red-800 dark:text-red-200 mb-1">
                    {t(I18N_KEYS.BILLING.WARNING_CANCELLATION)}
                  </p>
                  <p className="text-xs text-red-700 dark:text-red-300">
                    {t(I18N_KEYS.BILLING.CANCELLATION_WARNING_TEXT)}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <Button
                variant="destructive"
                className="w-full md:w-auto"
                onClick={handleCancel}
                disabled={canceling}
              >
                {canceling
                  ? t(I18N_KEYS.COMMON.LOADING)
                  : t(I18N_KEYS.BILLING.CANCEL_SUBSCRIPTION)}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
