import { Plan, PlanFeature } from '@b6ai/shared';

/**
 * Get unique features for a plan compared to lower-tier plans
 * @param currentPlan The plan to get features for
 * @param allPlans All plans sorted by price (ascending)
 * @returns Features unique to this plan and included features count
 */
export function getDifferentialFeatures(
  currentPlan: Plan,
  allPlans: Plan[]
): {
  uniqueFeatures: PlanFeature[];
  includedFeatures: PlanFeature[];
  previousPlanFeatureCount: number;
} {
  // Find the index of current plan
  const currentPlanIndex = allPlans.findIndex((p) => p.id === currentPlan.id);

  // Get all features from lower-tier plans
  const lowerTierFeatures = new Set<string>();

  for (let i = 0; i < currentPlanIndex; i++) {
    allPlans[i].planFeatures.forEach((feature) => {
      // Use feature.value or feature.feature.name as unique identifier
      const featureKey = feature.feature?.name || feature.value;
      lowerTierFeatures.add(featureKey);
    });
  }

  // Separate unique and included features
  const uniqueFeatures: PlanFeature[] = [];
  const includedFeatures: PlanFeature[] = [];

  currentPlan.planFeatures.forEach((feature) => {
    const featureKey = feature.feature?.name || feature.value;
    if (lowerTierFeatures.has(featureKey)) {
      includedFeatures.push(feature);
    } else {
      uniqueFeatures.push(feature);
    }
  });

  return {
    uniqueFeatures,
    includedFeatures,
    previousPlanFeatureCount: lowerTierFeatures.size,
  };
}
