import { useState, useCallback } from 'react';
import {
  useGetBusinessHoursByTenantQuery,
  useGetHolidaysByTenantQuery,
  useGetOutOfOfficeSettingsByTenantQuery,
  useCreateBusinessHoursMutation,
  useUpdateBusinessHoursMutation,
  useDeleteBusinessHoursMutation,
  useCreateHolidayMutation,
  useUpdateHolidayMutation,
  useDeleteHolidayMutation,
  useCreateOrUpdateOutOfOfficeSettingsMutation,
  BusinessHoursCreateDto,
  Holiday,
  OutOfOfficeSettingsDto,
} from '@b6ai/shared';

// Mock tenant ID - in real app this would come from auth context
const MOCK_TENANT_ID = 'tenant-123';

export const useBusinessHours = () => {
  const [selectedScheduleId, setSelectedScheduleId] = useState<string | null>(null);

  // Queries
  const {
    data: businessHours = [],
    isLoading: isLoadingBusinessHours,
    error: businessHoursError,
    refetch: refetchBusinessHours,
  } = useGetBusinessHoursByTenantQuery(MOCK_TENANT_ID);

  const {
    data: holidays = [],
    isLoading: isLoadingHolidays,
    error: holidaysError,
    refetch: refetchHolidays,
  } = useGetHolidaysByTenantQuery({ tenantId: MOCK_TENANT_ID });

  const {
    data: outOfOfficeSettings,
    isLoading: isLoadingOutOfOffice,
    error: outOfOfficeError,
    refetch: refetchOutOfOffice,
  } = useGetOutOfOfficeSettingsByTenantQuery(MOCK_TENANT_ID);

  // Mutations
  const [createBusinessHours, { isLoading: isCreatingBusinessHours }] = useCreateBusinessHoursMutation();
  const [updateBusinessHours, { isLoading: isUpdatingBusinessHours }] = useUpdateBusinessHoursMutation();
  const [deleteBusinessHours, { isLoading: isDeletingBusinessHours }] = useDeleteBusinessHoursMutation();
  
  const [createHoliday, { isLoading: isCreatingHoliday }] = useCreateHolidayMutation();
  const [updateHoliday, { isLoading: isUpdatingHoliday }] = useUpdateHolidayMutation();
  const [deleteHoliday, { isLoading: isDeletingHoliday }] = useDeleteHolidayMutation();
  
  const [createOrUpdateOutOfOffice, { isLoading: isUpdatingOutOfOffice }] = useCreateOrUpdateOutOfOfficeSettingsMutation();

  // Business Hours operations
  const handleCreateBusinessHours = useCallback(async (data: Omit<BusinessHoursCreateDto, 'tenantId'>) => {
    try {
      await createBusinessHours({
        ...data,
        tenantId: MOCK_TENANT_ID,
      }).unwrap();
      refetchBusinessHours();
      return { success: true };
    } catch (error) {
      console.error('Failed to create business hours:', error);
      return { success: false, error };
    }
  }, [createBusinessHours, refetchBusinessHours]);

  const handleUpdateBusinessHours = useCallback(async (id: string, data: Partial<BusinessHoursCreateDto>) => {
    try {
      await updateBusinessHours({ id, data }).unwrap();
      refetchBusinessHours();
      return { success: true };
    } catch (error) {
      console.error('Failed to update business hours:', error);
      return { success: false, error };
    }
  }, [updateBusinessHours, refetchBusinessHours]);

  const handleDeleteBusinessHours = useCallback(async (id: string) => {
    try {
      await deleteBusinessHours(id).unwrap();
      refetchBusinessHours();
      return { success: true };
    } catch (error) {
      console.error('Failed to delete business hours:', error);
      return { success: false, error };
    }
  }, [deleteBusinessHours, refetchBusinessHours]);

  // Holiday operations
  const handleCreateHoliday = useCallback(async (data: Omit<Holiday, 'id' | 'tenantId'>) => {
    try {
      await createHoliday({
        ...data,
        tenantId: MOCK_TENANT_ID,
      }).unwrap();
      refetchHolidays();
      return { success: true };
    } catch (error) {
      console.error('Failed to create holiday:', error);
      return { success: false, error };
    }
  }, [createHoliday, refetchHolidays]);

  const handleUpdateHoliday = useCallback(async (id: string, data: Partial<Holiday>) => {
    try {
      await updateHoliday({ id, data }).unwrap();
      refetchHolidays();
      return { success: true };
    } catch (error) {
      console.error('Failed to update holiday:', error);
      return { success: false, error };
    }
  }, [updateHoliday, refetchHolidays]);

  const handleDeleteHoliday = useCallback(async (id: string) => {
    try {
      await deleteHoliday(id).unwrap();
      refetchHolidays();
      return { success: true };
    } catch (error) {
      console.error('Failed to delete holiday:', error);
      return { success: false, error };
    }
  }, [deleteHoliday, refetchHolidays]);

  // Out of Office operations
  const handleUpdateOutOfOfficeSettings = useCallback(async (data: Omit<OutOfOfficeSettingsDto, 'tenantId'>) => {
    try {
      await createOrUpdateOutOfOffice({
        ...data,
        tenantId: MOCK_TENANT_ID,
      }).unwrap();
      refetchOutOfOffice();
      return { success: true };
    } catch (error) {
      console.error('Failed to update out of office settings:', error);
      return { success: false, error };
    }
  }, [createOrUpdateOutOfOffice, refetchOutOfOffice]);

  // Computed values
  const isLoading = isLoadingBusinessHours || isLoadingHolidays || isLoadingOutOfOffice;
  const hasError = businessHoursError || holidaysError || outOfOfficeError;
  const isMutating = isCreatingBusinessHours || isUpdatingBusinessHours || isDeletingBusinessHours ||
                     isCreatingHoliday || isUpdatingHoliday || isDeletingHoliday || isUpdatingOutOfOffice;

  const defaultBusinessHours = businessHours.find(bh => bh.isDefault);

  return {
    // Data
    businessHours,
    holidays,
    outOfOfficeSettings,
    defaultBusinessHours,
    selectedScheduleId,
    
    // Loading states
    isLoading,
    isMutating,
    hasError,
    
    // Actions
    setSelectedScheduleId,
    handleCreateBusinessHours,
    handleUpdateBusinessHours,
    handleDeleteBusinessHours,
    handleCreateHoliday,
    handleUpdateHoliday,
    handleDeleteHoliday,
    handleUpdateOutOfOfficeSettings,
    
    // Refetch functions
    refetchBusinessHours,
    refetchHolidays,
    refetchOutOfOffice,
  };
};
