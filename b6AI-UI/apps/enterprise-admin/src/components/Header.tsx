'use client';

import React, { FC, useEffect, useState } from 'react';
import {
  Bell,
  Sun,
  Moon,
  Globe,
  ChevronDown,
  MessageSquare,
  AlertTriangle,
  Clock,
  X,
} from 'lucide-react';

import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  Separator,
} from '@b6ai/ui';
import {
  I18N_KEYS,
  useLocalization,
} from '../app/context/localization-context';
import { LANGUAGES } from '@b6ai/shared';

interface Notification {
  id: number;
  type: 'message' | 'alert' | 'warning';
  title: string;
  description: string;
  time: string;
}

const notifications: Notification[] = [];

export const Header: FC = () => {
  const [showNotifications, setShowNotifications] = useState(false);

  const [isDark, setIsDark] = useState(false);
  useEffect(() => {
    if (typeof document !== 'undefined') {
      setIsDark(document.documentElement.classList.contains('dark'));
    }
  }, []);
  const toggleTheme = () => {
    if (typeof document !== 'undefined') {
      if (document.documentElement.classList.contains('dark')) {
        document.documentElement.classList.remove('dark');
        setIsDark(false);
      } else {
        document.documentElement.classList.add('dark');
        setIsDark(true);
      }
    }
  };

  const { currentLanguage, setLanguage, t } = useLocalization();
  const languages = Object.entries(LANGUAGES).map(([code, { name, flag }]) => ({
    code,
    label: `${flag} ${name}`,
  }));

  const getIcon = (type: string) => {
    switch (type) {
      case 'message':
        return <MessageSquare className="h-4 w-4 text-primary" />;
      case 'alert':
        return <Bell className="h-4 w-4 text-destructive" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-accent" />;
      default:
        return null;
    }
  };

  return (
    <header className="h-16 border-b border-border bg-background text-foreground flex items-center justify-end px-6">
      <div className="flex items-center space-x-4">
        <div className="relative">
          <Button
            variant="ghost"
            size="icon"
            className="p-2 rounded-full hover:bg-light-muted dark:hover:bg-dark-muted transition-colors"
            onClick={() => setShowNotifications(!showNotifications)}
            aria-label={t(I18N_KEYS.NOTIFICATIONS.VIEW_ALL)}
          >
            <Bell className="h-5 w-5" aria-hidden="true" />
            <span className="absolute top-2 right-2 h-2 w-2 rounded-full bg-destructive" />
          </Button>

          {showNotifications && (
            <div className="absolute right-0 mt-3 w-72 z-50">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-base font-semibold">
                    {t(I18N_KEYS.NOTIFICATIONS.VIEW_ALL)}
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowNotifications(false)}
                  >
                    <X size={16} />
                  </Button>
                </CardHeader>
                <Separator />
                <CardContent className="p-0">
                  {notifications.length === 0 ? (
                    <div className="p-4 text-center text-sm text-muted-foreground">
                      {t(I18N_KEYS.NOTIFICATIONS.NO_NOTIFICATIONS)}
                    </div>
                  ) : (
                    notifications.map((n, idx) => (
                      <div key={n.id}>
                        <div className="flex items-start gap-1 p-2">
                          {getIcon(n.type)}
                          <div className="flex-1">
                            <CardTitle className="text-sm font-medium">
                              {n.title}
                            </CardTitle>
                            <CardDescription className="text-sm">
                              {n.description}
                            </CardDescription>
                            <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
                              <Clock className="h-3 w-3" />
                              {n.time}
                            </div>
                          </div>
                        </div>
                        {idx !== notifications.length - 1 && <Separator />}
                      </div>
                    ))
                  )}
                </CardContent>

                <Separator />
                {notifications.length > 3 && (
                  <CardContent className="p-1 text-center">
                    <Button variant="link" className="text-primary">
                      {t(I18N_KEYS.NOTIFICATIONS.VIEW_ALL)}
                    </Button>
                  </CardContent>
                )}
              </Card>
            </div>
          )}
        </div>

        <Button
          variant="ghost"
          size="icon"
          onClick={toggleTheme}
          className="relative rounded-full"
          aria-hidden="true"
        >
          {isDark ? (
            <Moon className="h-5 w-5 dark:text-b6ai-blue" />
          ) : (
            <Sun className="h-5 w-5 dark:text-b6ai-cyan" />
          )}
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="gap-2 rounded-full px-4 py-2 hover:bg-muted dark:hover:bg-muted-foreground"
            >
              <Globe className="h-5 w-5 dark:text-foreground" />
              <span className="text-sm font-medium">
                {languages.find((l) => l.code === currentLanguage)?.label}
              </span>
              <ChevronDown className="h-4 w-4 text-muted-foreground" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="mt-2 bg-card text-card-foreground dark:bg-card dark:text-card-foreground rounded-lg shadow-lg border border-border">
            <DropdownMenuLabel className="text-xs font-semibold px-3 py-1 text-muted-foreground">
              {t(I18N_KEYS.LANGUAGES.SELECT)}
            </DropdownMenuLabel>
            <DropdownMenuSeparator className="bg-border" />
            {languages.map((lang) => (
              <DropdownMenuItem
                key={lang.code}
                onSelect={() => setLanguage(lang.code)}
                className="cursor-pointer hover:bg-muted hover:dark:bg-muted-foreground px-3 py-2 rounded-md"
              >
                {lang.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};
