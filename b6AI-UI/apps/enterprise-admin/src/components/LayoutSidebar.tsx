'use client';

import React, { useEffect, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import {
  LayoutDashboardIcon,
  MessageSquareIcon,
  BotIcon,
  UsersIcon,
  ActivityIcon,
  SettingsIcon,
  LogOutIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from 'lucide-react';
import { LogoutModal } from './modals/LogoutModal';
import { useAuth } from '../context/AuthContext';
import Image from 'next/image';
import {
  I18N_KEYS,
  useLocalization,
} from '../app/context/localization-context';
import { useAuditLogger } from '@b6ai/shared';

interface SidebarItemProps {
  icon: React.ReactNode;
  label: string;
  active?: boolean;
  onClick?: () => void;
  collapsed?: boolean;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
  icon,
  label,
  active = false,
  onClick,
  collapsed = false,
}) => {
  return (
    <button
      type="button"
      aria-current={active ? 'page' : undefined}
      className={`flex w-full items-center ${
        collapsed ? 'justify-center' : 'px-4'
      } py-3 cursor-pointer rounded hover:bg-light-muted dark:hover:bg-dark-muted transition-colors ${
        active
          ? 'bg-light-muted dark:bg-dark-muted text-b6ai-blue dark:text-b6ai-cyan'
          : 'text-light-foreground dark:text-dark-foreground'
      }`}
      onClick={onClick}
    >
      <span className={`${collapsed ? 'w-5 h-5' : 'w-5 h-5 mr-3'}`}>
        {icon}
      </span>
      {!collapsed && <span className="text-sm font-medium">{label}</span>}
    </button>
  );
};

export const Sidebar: React.FC = () => {
  const router = useRouter();
  const pathname = usePathname();
  const { logout } = useAuth();
  const { t } = useLocalization();
  const { logNavigation, logAuth } = useAuditLogger();

  const [activeItem, setActiveItem] = useState('Dashboard');
  const [collapsed, setCollapsed] = useState(false);
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);

  // Update active item based on current route
  useEffect(() => {
    if (pathname === '/') {
      setActiveItem('Dashboard');
    } else if (pathname === '/chat') {
      setActiveItem('Chats');
    } else if (pathname?.includes('/bots')) {
      setActiveItem('Bots');
    } else if (pathname?.includes('/teams')) {
      setActiveItem('Teams');
    } else if (pathname?.includes('/activity-logs')) {
      setActiveItem('Activity Logs');
    } else if (pathname?.includes('/settings')) {
      setActiveItem('Settings');
    }
  }, [pathname]);

  const handleItemClick = (key: string) => {
    setActiveItem(key);

    let route = '/';
    let pageName = 'Dashboard';

    switch (key) {
      case I18N_KEYS.NAV.DASHBOARD:
        route = '/';
        pageName = 'Dashboard';
        break;
      case I18N_KEYS.NAV.CHATS:
        route = '/chat';
        pageName = 'Chats';
        break;
      case I18N_KEYS.NAV.BOTS:
        route = '/bots';
        pageName = 'Bots';
        break;
      case I18N_KEYS.NAV.TEAMS:
        route = '/teams';
        pageName = 'Teams';
        break;
      case I18N_KEYS.NAV.ACTIVITY_LOGS:
        route = '/activity-logs';
        pageName = 'Activity Logs';
        break;
      case I18N_KEYS.NAV.SETTINGS:
        route = '/settings';
        pageName = 'Settings';
        break;
      default:
        break;
    }

    // // Log navigation
    // logNavigation('Page Navigation', `Navigated to ${pageName}`, {
    //   from: pathname,
    //   to: route,
    //   page: pageName,
    // });

    router.push(route);
  };

  const toggleCollapse = () => {
    setCollapsed(!collapsed);
  };

  const handleLogout = async () => {
    // Log logout event
    await logAuth('User Logout', 'User logged out from the application');
    logout();
    setIsLogoutModalOpen(false);
  };

  return (
    <>
      <div
        className={`${
          collapsed ? 'w-16' : 'w-64'
        } h-screen flex flex-col bg-light-card dark:bg-dark-card border-r border-light-border dark:border-dark-border transition-all duration-300`}
      >
        {/* Header with logo */}
        <div className="relative flex items-center border-b border-light-border dark:border-dark-border h-16">
          {!collapsed && (
            <div className="flex items-center p-2">
              <Image
                src="/images/horizontal logo.png"
                alt="b6ai logo"
                width={150}
                height={152}
                className="object-contain"
              />
            </div>
          )}
          {collapsed && (
            <div className="w-10 h-10 mx-auto relative">
              <Image
                src="/images/b6ai dark transparent.png"
                alt="b6ai logo"
                fill
                className="object-contain"
              />
            </div>
          )}

          {/* Collapse Button */}
          <button
            onClick={toggleCollapse}
            className="absolute top-1/2 -translate-y-1/2 right-0 translate-x-1/2 p-1 rounded hover:bg-light-muted dark:hover:bg-dark-muted"
            aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {collapsed ? (
              <ChevronRightIcon size={18} />
            ) : (
              <ChevronLeftIcon size={18} />
            )}
          </button>
        </div>
        <div className=" p-3">
          {/* Nav Items */}
          <div className="flex-1 overflow-y-auto py-8">
            <nav className={`space-y-1 ${collapsed ? 'px-2' : 'px-2'}`}>
              <SidebarItem
                icon={<LayoutDashboardIcon size={20} />}
                label={t(I18N_KEYS.NAV.DASHBOARD)}
                active={activeItem === I18N_KEYS.NAV.DASHBOARD}
                onClick={() => handleItemClick(I18N_KEYS.NAV.DASHBOARD)}
                collapsed={collapsed}
              />
              <SidebarItem
                icon={<MessageSquareIcon size={20} />}
                label={t(I18N_KEYS.NAV.CHATS)}
                active={activeItem === I18N_KEYS.NAV.CHATS}
                onClick={() => handleItemClick(I18N_KEYS.NAV.CHATS)}
                collapsed={collapsed}
              />

              <SidebarItem
                icon={<BotIcon size={20} />}
                label={t(I18N_KEYS.NAV.BOTS)}
                active={activeItem === I18N_KEYS.NAV.BOTS}
                onClick={() => handleItemClick(I18N_KEYS.NAV.BOTS)}
                collapsed={collapsed}
              />
              <SidebarItem
                icon={<UsersIcon size={20} />}
                label={t(I18N_KEYS.NAV.TEAMS)}
                active={activeItem === I18N_KEYS.NAV.TEAMS}
                onClick={() => handleItemClick(I18N_KEYS.NAV.TEAMS)}
                collapsed={collapsed}
              />
              <SidebarItem
                icon={<ActivityIcon size={20} />}
                label={t(I18N_KEYS.NAV.ACTIVITY_LOGS)}
                active={activeItem === I18N_KEYS.NAV.ACTIVITY_LOGS}
                onClick={() => handleItemClick(I18N_KEYS.NAV.ACTIVITY_LOGS)}
                collapsed={collapsed}
              />
              <SidebarItem
                icon={<SettingsIcon size={20} />}
                label={t(I18N_KEYS.NAV.SETTINGS)}
                active={activeItem === I18N_KEYS.NAV.SETTINGS}
                onClick={() => handleItemClick(I18N_KEYS.NAV.SETTINGS)}
                collapsed={collapsed}
              />
            </nav>
          </div>

          {/* Logout */}
          <div
            className={`px-2 py-4 border-t border-light-border dark:border-dark-border ${
              collapsed ? 'flex justify-center' : ''
            }`}
          >
            <SidebarItem
              icon={<LogOutIcon size={20} />}
              label={t(I18N_KEYS.NAV.LOGOUT)}
              onClick={() => setIsLogoutModalOpen(true)}
              collapsed={collapsed}
            />
          </div>
        </div>
      </div>

      {/* Logout Confirmation Modal */}
      <LogoutModal
        isOpen={isLogoutModalOpen}
        onClose={() => setIsLogoutModalOpen(false)}
        onConfirm={handleLogout}
      />
    </>
  );
};
