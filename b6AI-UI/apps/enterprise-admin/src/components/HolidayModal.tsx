import React, { useState } from 'react';
import { XIcon, SaveIcon, LoaderIcon } from 'lucide-react';

interface HolidayModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (holiday: { name: string; date: Date; type: string }) => Promise<{ success: boolean }>;
  isLoading?: boolean;
}

export const HolidayModal: React.FC<HolidayModalProps> = ({
  isOpen,
  onClose,
  onSave,
  isLoading = false,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    date: '',
    type: 'custom',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.date) {
      return;
    }

    const result = await onSave({
      name: formData.name,
      date: new Date(formData.date),
      type: formData.type,
    });

    if (result.success) {
      setFormData({ name: '', date: '', type: 'custom' });
      onClose();
    }
  };

  const handleClose = () => {
    setFormData({ name: '', date: '', type: 'custom' });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-light-card dark:bg-dark-card rounded-lg border border-light-border dark:border-dark-border w-full max-w-md mx-4">
        <div className="flex items-center justify-between p-4 border-b border-light-border dark:border-dark-border">
          <h3 className="text-lg font-medium text-light-foreground dark:text-dark-foreground">
            Add Holiday
          </h3>
          <button
            onClick={handleClose}
            className="text-light-muted-foreground dark:text-dark-muted-foreground hover:text-light-foreground dark:hover:text-dark-foreground"
          >
            <XIcon size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4">
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2 text-light-foreground dark:text-dark-foreground">
              Holiday Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="e.g., Christmas Day"
              className="w-full px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
              required
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium mb-2 text-light-foreground dark:text-dark-foreground">
              Date
            </label>
            <input
              type="date"
              value={formData.date}
              onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
              className="w-full px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
              required
            />
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium mb-2 text-light-foreground dark:text-dark-foreground">
              Type
            </label>
            <select
              value={formData.type}
              onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
              className="w-full px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
            >
              <option value="custom">Custom</option>
              <option value="global">Global</option>
              <option value="us">US</option>
              <option value="uk">UK</option>
              <option value="ca">Canada</option>
            </select>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-sm border border-light-border dark:border-dark-border rounded hover:bg-light-muted dark:hover:bg-dark-muted transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || !formData.name || !formData.date}
              className="flex items-center px-4 py-2 text-sm bg-b6ai-blue dark:bg-b6ai-cyan text-white rounded hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <LoaderIcon size={16} className="mr-2 animate-spin" />
              ) : (
                <SaveIcon size={16} className="mr-2" />
              )}
              {isLoading ? 'Adding...' : 'Add Holiday'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
