'use client';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  G<PERSON>Branch,
  Bell,
  <PERSON>,
  Puzzle,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>lette,
  Home,
} from 'lucide-react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@b6ai/ui';
import Link from 'next/link';
import { I18N_KEYS } from '@b6ai/shared';
import { useLocalization } from '../../context/localization-context';

export default function SettingsPage() {
  const { t } = useLocalization();

  const settingsItems = [
    {
      title: t(I18N_KEYS.SETTINGS.GENERAL.TITLE),
      description: t(I18N_KEYS.SETTINGS.GENERAL.DESCRIPTION),
      icon: Settings,
      href: '/settings/general',
    },
    {
      title: t(I18N_KEYS.SETTINGS.BUSINESS_HOURS.TITLE),
      description: t(I18N_KEYS.SETTINGS.BUSINESS_HOURS.DESCRIPTION),
      icon: Clock,
      href: '/settings/business-hours',
    },
    {
      title: t(I18N_KEYS.SETTINGS.ROUTING_RULES.TITLE),
      description: t(I18N_KEYS.SETTINGS.ROUTING_RULES.DESCRIPTION),
      icon: GitBranch,
      href: '/settings/routing-rules',
    },
    {
      title: t(I18N_KEYS.SETTINGS.NOTIFICATIONS.TITLE),
      description: t(I18N_KEYS.SETTINGS.NOTIFICATIONS.DESCRIPTION),
      icon: Bell,
      href: '/settings/notifications',
    },
    {
      title: t(I18N_KEYS.SETTINGS.SECURITY.TITLE),
      description: t(I18N_KEYS.SETTINGS.SECURITY.DESCRIPTION),
      icon: Shield,
      href: '/settings/security',
    },
    {
      title: t(I18N_KEYS.SETTINGS.INTEGRATIONS.TITLE),
      description: t(I18N_KEYS.SETTINGS.INTEGRATIONS.DESCRIPTION),
      icon: Puzzle,
      href: '/settings/integrations',
    },
    {
      title: t(I18N_KEYS.SETTINGS.REPORTING.TITLE),
      description: t(I18N_KEYS.SETTINGS.REPORTING.DESCRIPTION),
      icon: BarChart3,
      href: '/settings/reporting',
    },
    {
      title: t(I18N_KEYS.SETTINGS.BILLING.TITLE),
      description: t(I18N_KEYS.SETTINGS.BILLING.DESCRIPTION),
      icon: CreditCard,
      href: '/settings/billing-subscription',
    },
    {
      title: t(I18N_KEYS.SETTINGS.BRANDING.TITLE),
      description: t(I18N_KEYS.SETTINGS.BRANDING.DESCRIPTION),
      icon: Palette,
      href: '/settings/branding',
    },
  ];

  return (
    <div className="w-full">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink
              href="/dashboard"
              className="flex items-center gap-1"
            >
              <Home size={16} />
              {t(I18N_KEYS.SETTINGS.HOME)}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{t(I18N_KEYS.SETTINGS.TITLE)}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Title */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-light-foreground dark:text-dark-foreground">
          {t(I18N_KEYS.SETTINGS.TITLE)}
        </h1>
        <p className="text-sm text-muted-foreground dark:text-dark-muted-foreground mt-1">
          {t(I18N_KEYS.SETTINGS.DESCRIPTION)}
        </p>
      </div>

      {/* Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
        {settingsItems.map((item) => (
          <Link key={item.title} href={item.href}>
            <Card className="h-40 w-72 cursor-pointer hover:shadow-sm transition-shadow border border-light-border dark:border-dark-border flex flex-col">
              <CardHeader className="pb-2">
                <div className="flex flex-col items-start space-y-2">
                  <item.icon className="h-6 w-6 text-b6ai-blue dark:text-b6ai-cyan" />
                  <CardTitle
                    className="text-lg max-w-64 font-semibold truncate"
                    title={item.title}
                  >
                    {item.title}
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent className="flex-1 overflow-hidden">
                <p
                  className="text-sm text-muted-foreground dark:text-dark-muted-foreground leading-snug overflow-hidden text-ellipsis"
                  style={{
                    display: '-webkit-box',
                    WebkitLineClamp: 3,
                    WebkitBoxOrient: 'vertical',
                  }}
                  title={item.description}
                >
                  {item.description}
                </p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}
