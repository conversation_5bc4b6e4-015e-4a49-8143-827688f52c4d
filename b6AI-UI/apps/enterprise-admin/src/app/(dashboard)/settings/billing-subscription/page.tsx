'use client';

import React, { useState } from 'react';
import {
  Plan,
  PLAN_QUERY_FILTER,
  BILLING_PERIOD,
  useCheckoutSubscriptionMutation,
  useGetAllPlansQuery,
  BillingCustomer,
  useCancelSubscriptionMutation,
  useGetCurrentSubscriptionQuery,
} from '@b6ai/shared';
import { Loader } from '@b6ai/ui';
import { BillingPage } from '../../../../views/billing/BillingPage';

const dummyCustomer: BillingCustomer = {
  firstName: 'test-user',
  lastName: 'test-user',
  email: '<EMAIL>',
  tenantId: '002d766a-d1e1-d2e2-3bb9-4c08a5dcf1da',
};

const SubscriptionPage: React.FC = () => {
  const [billingPeriod, setBillingPeriod] = useState<BILLING_PERIOD>(
    BILLING_PERIOD.YEARLY
  );
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);

  // Fetch current subscription
  const { data: subscription, isLoading: subscriptionLoading } =
    useGetCurrentSubscriptionQuery(dummyCustomer.tenantId);

  const [canceling, setCanceling] = useState(false);
  const [cancelSubscription] = useCancelSubscriptionMutation();

  const handleCancel = async () => {
    if (!subscription?.id) return;
    setCanceling(true);
    try {
      await cancelSubscription({ subscriptionId: subscription.id }).unwrap();
      alert('Subscription canceled successfully');
    } catch (err) {
      console.error(err);
      alert('Failed to cancel subscription');
    } finally {
      setCanceling(false);
    }
  };

  const {
    data: plans,
    isLoading: plansLoading,
    isFetching: plansFetching,
  } = useGetAllPlansQuery(PLAN_QUERY_FILTER);

  const [checkout, { isLoading }] = useCheckoutSubscriptionMutation();

  const onGetStarted = (plan: Plan) => {
    setSelectedPlan(plan);
    checkout({
      planId: plan.planCode,
      period: billingPeriod,
      ...dummyCustomer,
    });
  };

  return (
    <div className="w-full flex flex-col min-h-full">
      {plansFetching || plansLoading || subscriptionLoading ? (
        <Loader />
      ) : (
        <BillingPage
          plans={plans ?? []}
          billingPeriod={billingPeriod}
          setBillingPeriod={setBillingPeriod}
          onGetStarted={onGetStarted}
          isLoading={isLoading}
          selectedPlan={selectedPlan}
          subscription={subscription ?? null}
          canceling={canceling}
          handleCancel={handleCancel}
        />
      )}
    </div>
  );
};

export default SubscriptionPage;
