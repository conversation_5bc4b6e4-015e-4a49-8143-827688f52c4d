import './global.css';
import '@b6ai/ui/styles/globals.css';
import { LocalizationProvider } from './context/localization-context';
import { ToastProvider } from './context/toast-context';
// import { BrandProvider } from '@b6ai/shared';
import { BrandProvider } from '@b6ai/shared';
import React from 'react';
import { Roboto_Flex } from 'next/font/google';
// import { cn } from '@b6ai/ui/utils';
import { cn } from '@b6ai/ui/utils';
import { AuthProvider } from '../context/AuthContext';

const roboto = Roboto_Flex({
  subsets: ['latin'],
  variable: '--font-roboto', // expose CSS var
  weight: ['400', '500', '700'], // choose what you need
});

export const metadata = {
  title: 'Welcome to enterprise-admin',
  description: 'Generated by create-nx-workspace',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={cn(roboto.variable)}>
      <body>
        <BrandProvider>
          <LocalizationProvider>
            <ToastProvider>
              <AuthProvider>{children}</AuthProvider>
            </ToastProvider>
          </LocalizationProvider>
        </BrandProvider>
      </body>
    </html>
  );
}
