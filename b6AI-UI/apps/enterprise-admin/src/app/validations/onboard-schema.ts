import { z } from 'zod';

export const companySchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(1, 'Last name is required'),
  tenantName: z.string().min(3, 'Company name must be at least 3 characters'),
  contactEmail: z.string().email('Invalid email'),
  domain: z
    .string()
    .regex(
      /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]$/,
      'Invalid subdomain format'
    ),
});

export type CompanyFormValues = z.infer<typeof companySchema>;
