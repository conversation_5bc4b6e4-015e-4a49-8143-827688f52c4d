'use client';
import React, { useCallback, useState, createContext, useContext } from 'react';
import { ToastContainer, ToastItem } from '@b6ai/ui';
import { useLocalization } from './localization-context';

type ToastType =
  | 'success'
  | 'error'
  | 'warning'
  | 'info'
  | 'gradient'
  | 'custom';

interface ToastContextType {
  showToast: (
    type: ToastType,
    message: string,
    duration?: number,
    customClass?: string
  ) => void;
  hideToast: (id: string) => void;
  success: (message: string, duration?: number) => void;
  error: (message: string, duration?: number) => void;
  warning: (message: string, duration?: number) => void;
  info: (message: string, duration?: number) => void;
  gradient: (message: string, duration?: number) => void;
  custom: (message: string, customClass: string, duration?: number) => void;
}
const ToastContext = createContext<ToastContextType | undefined>(undefined);
export const ToastProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const [toasts, setToasts] = useState<
    (ToastItem & {
      customClass?: string;
    })[]
  >([]);
  const { t } = useLocalization();
  const showToast = useCallback(
    (
      type: ToastType,
      message: string,
      duration = 5000,
      customClass?: string
    ) => {
      const id = Date.now().toString();
      setToasts((prevToasts) => [
        ...prevToasts,
        {
          id,
          type,
          message: t(message),
          duration,
          customClass,
        },
      ]);
      return id;
    },
    [t]
  );
  const hideToast = useCallback((id: string) => {
    setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id));
  }, []);
  const success = useCallback(
    (message: string, duration?: number) =>
      showToast('success', message, duration),
    [showToast]
  );
  const error = useCallback(
    (message: string, duration?: number) =>
      showToast('error', message, duration),
    [showToast]
  );
  const warning = useCallback(
    (message: string, duration?: number) =>
      showToast('warning', message, duration),
    [showToast]
  );
  const info = useCallback(
    (message: string, duration?: number) =>
      showToast('info', message, duration),
    [showToast]
  );
  const gradient = useCallback(
    (message: string, duration?: number) =>
      showToast('gradient', message, duration),
    [showToast]
  );
  const custom = useCallback(
    (message: string, customClass: string, duration?: number) =>
      showToast('custom', message, duration, customClass),
    [showToast]
  );
  return (
    <ToastContext.Provider
      value={{
        showToast,
        hideToast,
        success,
        error,
        warning,
        info,
        gradient,
        custom,
      }}
    >
      {children}
      <ToastContainer toasts={toasts} removeToast={hideToast} />
    </ToastContext.Provider>
  );
};
export const useToast = () => {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};
