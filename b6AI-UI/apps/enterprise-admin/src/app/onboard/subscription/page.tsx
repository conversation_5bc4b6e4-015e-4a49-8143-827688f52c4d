'use client';

import React, { useState } from 'react';
import {
  Plan,
  PLAN_QUERY_FILTER,
  BILLING_PERIOD,
  useCheckoutSubscriptionMutation,
  useGetAllPlansQuery,
  BillingCustomer,
} from '@b6ai/shared';
import { PlanList } from '../../../views/subscription/PlansList';
import { Loader } from '@b6ai/ui';
const dummyCustomer: BillingCustomer = {
  firstName: 'test-user',
  lastName: 'test-user',
  email: '<EMAIL>',
  tenantId: '002d766a-d1e1-d2e2-3bb9-4c08a5dcf1da',
};
const SubscriptionPage: React.FC = () => {
  const [billingPeriod, setBillingPeriod] = useState<BILLING_PERIOD>(
    BILLING_PERIOD.YEARLY
  );

  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);

  const {
    data: plans,
    isLoading: plansLoading,
    isFetching: plansFetching,
  } = useGetAllPlansQuery(PLAN_QUERY_FILTER);

  const [checkout, { isLoading }] = useCheckoutSubscriptionMutation();

  const onGetStarted = (plan: Plan) => {
    setSelectedPlan(plan);
    checkout({
      planId: plan.planCode,
      period: billingPeriod,
      ...dummyCustomer,
    });
  };
  const onBookCall = (plan: Plan) => {
    console.log('book call', plan); // TODO implement later
  };

  return (
    <div className="bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 w-full page-x-padding  flex flex-col min-h-screen">
      {plansFetching || plansLoading ? (
        <Loader />
      ) : (
        <PlanList
          plans={plans ?? []}
          reccommendedPlanCode="STANDARD"
          billingPeriod={billingPeriod}
          setBillingPeriod={setBillingPeriod}
          onBookCall={onBookCall}
          onGetStarted={onGetStarted}
          isLoading={isLoading}
          selectedPlan={selectedPlan}
        />
      )}
    </div>
  );
};

export default SubscriptionPage;
