import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import SubscriptionPage from './page';

describe('SubscriptionPage', () => {
  it('renders the page with plans and toggle', () => {
    render(<SubscriptionPage />);
    expect(screen.getByText('Choose Your Plan')).toBeTruthy();
    expect(screen.getByText('Basic')).toBeTruthy();
    expect(screen.getByText('Pro')).toBeTruthy();
    expect(screen.getByText('Enterprise')).toBeTruthy();
    expect(screen.getByText('Monthly')).toBeTruthy();
    expect(screen.getByText('Yearly')).toBeTruthy();
  });

  it('toggles between monthly and yearly pricing', () => {
    render(<SubscriptionPage />);
    const monthlyButton = screen.getByText('Monthly');
    const yearlyButton = screen.getByText('Yearly');

    // Initially yearly is selected, so yearly button should have default variant
    expect(yearlyButton.className).toContain('bg-blue-600');

    fireEvent.click(monthlyButton);
    // After clicking monthly, monthly button should have default variant
    expect(monthlyButton.className).toContain('bg-blue-600');
  });

  it('selects a plan and logs it', () => {
    console.log = jest.fn();
    render(<SubscriptionPage />);
    const selectButtons = screen.getAllByText('Select Plan');
    fireEvent.click(selectButtons[0]);
    expect(console.log).toHaveBeenCalledWith(
      expect.stringContaining('Selected plan:')
    );
  });
});
