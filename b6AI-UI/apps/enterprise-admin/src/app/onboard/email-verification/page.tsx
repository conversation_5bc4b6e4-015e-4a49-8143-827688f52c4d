'use client';

import { useState, useRef, useEffect } from 'react';
import { In<PERSON>, <PERSON><PERSON>, Card, CardContent } from '@b6ai/ui';
import { ArrowLeft, Mail, AlertCircle } from 'lucide-react';

export default function EmailVerificationPage() {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [resendTimer, setResendTimer] = useState(0);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const [email, setEmail] = useState('');

  useEffect(() => {
    const storedEmail = localStorage.getItem('signupEmail');
    if (storedEmail) setEmail(storedEmail);
  }, []);

  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => setResendTimer(resendTimer - 1), 1000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [resendTimer]);

  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) return;
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    setError('');
    if (value && index < 5) inputRefs.current[index + 1]?.focus();
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').slice(0, 6);
    const newOtp = [...otp];
    for (let i = 0; i < pastedData.length && i < 6; i++) {
      if (/^\d$/.test(pastedData[i])) newOtp[i] = pastedData[i];
    }
    setOtp(newOtp);
  };

  const handleVerify = async () => {
    const otpCode = otp.join('');
    if (otpCode.length !== 6) {
      setError('Please enter all 6 digits');
      return;
    }
    setIsLoading(true);
    setError('');
    try {
      await new Promise((resolve) => setTimeout(resolve, 2000));
      if (otpCode === '123456') {
        localStorage.setItem('verifiedEmail', email);
        window.location.href = '/onboard/tenant-details'; // ✅ redirect immediately
      } else {
        setError('Invalid verification code. Please try again.');
      }
    } catch {
      setError('Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResend = async () => {
    setResendTimer(60);
    setError('');
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch {
      setError('Failed to resend code. Please try again.');
    }
  };

  // Framer Motion animation configs

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 p-8">
      <div className="w-full max-w-md z-10">
        <Card className="bg-white rounded-2xl shadow-lg relative ">
          <CardContent className="p-8">
            {/* Header */}
            <div className="text-center mb-8 relative">
              <Button
                variant="ghost"
                className="absolute top-2 left-4 p-2 text-primary hover:bg-white"
                onClick={() => window.history.back()}
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <Mail className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-2xl font-bold text-primary mb-2">
                Verify Your Email
              </h1>
              <p className="text-b6ai-gradient-bg">
                {`We've sent a 6-digit verification code to`}
              </p>
              <p className="text-white font-medium">{email}</p>
            </div>

            {/* OTP Inputs */}
            <div className="mb-6">
              <div className="flex gap-3 justify-center mb-4">
                {otp.map((digit, index) => (
                  <Input
                    key={index}
                    ref={(el) => {
                      inputRefs.current[index] = el;
                    }}
                    type="text"
                    inputMode="numeric"
                    maxLength={1}
                    value={digit}
                    onChange={(e) => handleOtpChange(index, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(index, e)}
                    onPaste={handlePaste}
                    className={`w-12 h-12 text-center text-lg font-semibold border-2 rounded-lg transition-all text-[#0a0f1c] bg-white ${
                      error
                        ? 'border-red-400 focus:border-red-500'
                        : 'border-b6ai-gradient-bg focus:border-b6ai-gradient-bg'
                    }`}
                  />
                ))}
              </div>

              {error && (
                <div className="flex items-center gap-2 text-red-400 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {error}
                </div>
              )}
            </div>

            {/* Verify Button */}
            <Button
              onClick={handleVerify}
              disabled={isLoading || otp.join('').length !== 6}
              className="w-full px-4 py-2 btn-b6ai rounded-md"
            >
              {isLoading ? 'Verifying...' : 'Verify Email'}
            </Button>

            {/* Resend */}
            <div className="text-center">
              <p className="text-gray-400 text-sm mb-2">
                {`Didn't receive the code?`}
              </p>
              <Button
                variant="link"
                onClick={handleResend}
                disabled={resendTimer > 0}
                className="p-2 bg-b6ai-gradient rounded-lg text-white"
              >
                {resendTimer > 0
                  ? `Resend in ${resendTimer}s`
                  : 'Resend verification code'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
