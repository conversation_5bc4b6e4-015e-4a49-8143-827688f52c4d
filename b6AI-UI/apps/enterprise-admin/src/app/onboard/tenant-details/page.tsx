'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useForm, ControllerRenderProps } from 'react-hook-form';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, Building, CheckCircle, User, Globe } from 'lucide-react';
import { useOnboardTenantMutation, OnboardTenantDto } from '@b6ai/shared';
import { FormDescription, FormField, FormMessage, Input, Form } from '@b6ai/ui';
import { companySchema } from '../../validations/onboard-schema';

type CompanyFormValues = z.infer<typeof companySchema>;

function CompanyDetailsPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [email, setEmail] = useState<string | null>(null);
  const [onboardTenant, { isLoading, isError, isSuccess }] =
    useOnboardTenantMutation();

  useEffect(() => {
    const storedEmail = localStorage.getItem('verifiedEmail');
    const paramEmail = searchParams?.get('email');
    if (!storedEmail && !paramEmail) {
      router.push('/onboard');
      return;
    }
    setEmail(storedEmail);

    const isPageReload =
      performance.navigation.type === performance.navigation.TYPE_RELOAD;
    if (isPageReload) {
      localStorage.removeItem('verifiedEmail');
      router.push('/onboard');
    }
  }, [router, searchParams]);

  const form = useForm<CompanyFormValues>({
    resolver: zodResolver(companySchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      tenantName: '',
      contactEmail: email || '',
      domain: '',
    },
  });

  useEffect(() => {
    if (email) {
      form.reset({ ...form.getValues(), contactEmail: email });
    }
  }, [email, form]);

  const onSubmit = async (data: CompanyFormValues) => {
    const payload: OnboardTenantDto = {
      firstName: data.firstName,
      lastName: data.lastName,
      adminEmail: data.contactEmail,
      tenantName: data.tenantName,
      customDomain: data.domain,
      idempotencyKey: crypto.randomUUID(),
    };
    await onboardTenant(payload).unwrap();
  };

  if (!email)
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p className="text-gray-500">Loading your email...</p>
      </div>
    );

  if (isSuccess)
    return (
      <div className="min-h-screen flex items-center justify-center p-8 bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="w-full max-w-md bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 text-center border-2 border-primary">
          <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-primary mb-4">
            Company Setup Complete!
          </h1>
          <p className="text-muted-foreground mb-8">
            Your company details have been saved. Continue to subscription.
          </p>
          <button
            className="w-full h-12 bg-gradient-to-r from-b6ai-blue to-b6ai-cyan text-white rounded-lg hover:opacity-90 transition-opacity"
            onClick={() => router.push('/onboard/subscription')}
          >
            Continue to Subscription
          </button>
        </div>
      </div>
    );

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800">
      <div className="flex-1 flex items-center justify-center py-12">
        <div className="w-full max-w-2xl p-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg">
          <div className="text-center mb-8 relative">
            <button
              className="absolute top-0 left-0 p-2 text-primary hover:bg-background rounded"
              onClick={() => window.history.back()}
            >
              <ArrowLeft size={20} />
            </button>
            <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <Building
                size={24}
                className="text-b6ai-blue dark:text-b6ai-cyan"
              />
            </div>
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              Create Your Tenant
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">
              Set up your B6AI workspace
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* First & Last Name */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {['firstName', 'lastName'].map((field) => (
                  <FormField
                    key={field}
                    control={form.control}
                    name={field as keyof CompanyFormValues}
                    render={({
                      field: f,
                    }: {
                      field: ControllerRenderProps<CompanyFormValues>;
                    }) => (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          {field === 'firstName' ? 'First Name' : 'Last Name'}{' '}
                          <span className="text-red-500">*</span>
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <User
                              size={18}
                              className="text-gray-500 dark:text-gray-400"
                            />
                          </div>
                          <Input
                            {...f}
                            placeholder={field === 'firstName' ? 'John' : 'Doe'}
                            className={`w-full pl-10 pr-4 py-3 rounded-lg border ${
                              form.formState.errors[
                                field as keyof CompanyFormValues
                              ]
                                ? 'border-red-500'
                                : 'border-gray-300 dark:border-gray-600'
                            } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan`}
                          />
                        </div>
                        <FormMessage />
                      </div>
                    )}
                  />
                ))}
              </div>

              {/* Company Name */}
              <FormField
                control={form.control}
                name="tenantName"
                render={({ field }) => (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Organization Name
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Building
                          size={18}
                          className="text-gray-500 dark:text-gray-400"
                        />
                      </div>
                      <Input
                        {...field}
                        placeholder="Acme Corporation"
                        className={`w-full pl-10 pr-4 py-3 rounded-lg border ${
                          form.formState.errors.tenantName
                            ? 'border-red-500'
                            : 'border-gray-300 dark:border-gray-600'
                        } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan`}
                      />
                    </div>
                    <FormMessage />
                  </div>
                )}
              />

              {/* Admin Email */}
              <FormField
                control={form.control}
                name="contactEmail"
                render={({ field }) => (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Admin Email
                    </label>
                    <Input
                      {...field}
                      readOnly
                      className="w-full px-4 py-3 rounded-lg border bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                    />
                    <FormMessage />
                  </div>
                )}
              />

              {/* Subdomain */}
              <FormField
                control={form.control}
                name="domain"
                render={({ field }) => (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Subdomain <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Globe
                          size={18}
                          className="text-gray-500 dark:text-gray-400"
                        />
                      </div>
                      <Input
                        {...field}
                        placeholder="your-company"
                        className={`w-full pl-10 pr-20 py-3 rounded-lg border ${
                          form.formState.errors.domain
                            ? 'border-red-500'
                            : 'border-gray-300 dark:border-gray-600'
                        } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan`}
                      />
                      <span className="absolute right-3 top-3 text-gray-500 dark:text-gray-400">
                        .b6ai.app
                      </span>
                    </div>
                    <FormDescription>
                      This will be your unique URL:{' '}
                      <span className="font-medium">
                        [your-subdomain].b6ai.app
                      </span>
                    </FormDescription>
                    <FormMessage />
                  </div>
                )}
              />

              {/* Submit */}
              <div>
                <button
                  type="submit"
                  disabled={form.formState.isSubmitting || isLoading}
                  className={`w-full flex items-center justify-center px-4 py-3 bg-gradient-to-r from-b6ai-blue to-b6ai-cyan text-white rounded-lg ${
                    form.formState.isSubmitting || isLoading
                      ? 'opacity-70 cursor-not-allowed'
                      : 'hover:opacity-90'
                  } transition-opacity`}
                >
                  {form.formState.isSubmitting || isLoading
                    ? 'Creating...'
                    : 'Continue to Select Plan'}
                </button>
                {isError && (
                  <p className="text-red-500 mt-2">
                    Something went wrong while creating tenant.
                  </p>
                )}
              </div>
            </form>
          </Form>
        </div>
      </div>

      <footer className="py-6">
        <div className="container mx-auto px-6">
          <p className="text-center text-sm text-gray-600 dark:text-gray-400">
            © 2023 B6AI. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}

export default function CompanyDetailsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <CompanyDetailsPageContent />
    </Suspense>
  );
}
