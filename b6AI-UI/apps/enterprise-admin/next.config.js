//@ts-check

// eslint-disable-next-line @typescript-eslint/no-var-requires
const { composePlugins, withNx } = require('@nx/next');

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  // Use this to set Nx-specific options
  // See: https://nx.dev/recipes/next/next-config-setup
  nx: {},
  transpilePackages: ['@b6ai/ui', '@b6ai/shared'],
  webpack: (config, { isServer }) => {
    // Force webpack to resolve @b6ai/shared to source files
    config.resolve.alias = {
      ...config.resolve.alias,
      '@b6ai/shared$': require('path').resolve(
        __dirname,
        '../../libs/shared/src/index.ts'
      ),
      '@b6ai/shared': require('path').resolve(
        __dirname,
        '../../libs/shared/src'
      ),
      '@b6ai/ui$': require('path').resolve(
        __dirname,
        '../../libs/ui/src/index.ts'
      ),
      '@b6ai/ui': require('path').resolve(__dirname, '../../libs/ui/src'),
    };
    return config;
  },
};

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
];

module.exports = composePlugins(...plugins)(nextConfig);
