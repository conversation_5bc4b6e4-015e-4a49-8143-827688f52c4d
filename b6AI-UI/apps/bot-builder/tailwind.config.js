const { createGlobPatternsForDependencies } = require('@nx/react/tailwind');
const { join } = require('path');

/** @type {import('tailwindcss').Config} */
module.exports = {
  presets: [require('../../libs/ui/tailwind.config.js')],
  content: [
    join(
      __dirname,
      '{src,pages,components,app}/**/*!(*.stories|*.spec).{ts,tsx,html}'
    ),
    ...createGlobPatternsForDependencies(__dirname),
    // Manual path addition for UI library
    join(__dirname, '../../libs/ui/src/**/*.{ts,tsx}'),
  ],
  theme: {
    extend: {
      // Add any app-specific theme extensions here
    },
  },
  plugins: [
    // Add any app-specific plugins here
  ],
};
