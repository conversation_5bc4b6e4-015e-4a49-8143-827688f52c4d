{"name": "@b6-ai-ui/agent-portal", "version": "0.0.1", "private": true, "dependencies": {"@b6ai/shared": "workspace:*", "@b6ai/ui": "workspace:*", "lucide-react": "^0.543.0", "next": "~15.2.4", "react": "19.0.0", "react-dom": "19.0.0"}, "scripts": {"build": "next build", "dev": "next dev", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next"}, "devDependencies": {"autoprefixer": "10.4.13", "postcss": "8.4.38", "tailwindcss": "3.4.3", "tsconfig-paths-webpack-plugin": "^4.2.0"}}