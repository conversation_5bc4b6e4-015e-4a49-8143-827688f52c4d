import enTranslation from '../locales/en.json';
import arTranslation from '../locales/ar.json';
import esTranslation from '../locales/es.json';
import frTranslation from '../locales/fr.json';
import deTranslation from '../locales/de.json';
import ptTranslation from '../locales/pt.json';
import zhTranslation from '../locales/zh.json';
import jaTranslation from '../locales/ja.json';

export const I18N_RESOURCES = {
  en: {
    translation: enTranslation,
  },
  ar: {
    translation: arTranslation,
  },
  es: {
    translation: esTranslation,
  },
  fr: {
    translation: frTranslation,
  },
  de: {
    translation: deTranslation,
  },
  pt: {
    translation: ptTranslation,
  },
  zh: {
    translation: zhTranslation,
  },
  ja: {
    translation: jaTranslation,
  },
};

export type SupportedLanguage = keyof typeof I18N_RESOURCES;
