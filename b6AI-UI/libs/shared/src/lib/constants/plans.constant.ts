import { Filter, Plan } from '../types';
import { SOFT_DELETE_EXCLUDE } from './common.constant';

export const CURRENCY_SYMBOLS: Record<string, string> = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  JPY: '¥',
  CAD: 'C$',
  AUD: 'A$',
  INR: '₹',
  CNY: '¥',
  CHF: 'Fr',
  SEK: 'kr',
  NZD: 'NZ$',
  MXN: '$',
  SGD: 'S$',
  HKD: 'HK$',
  NOK: 'kr',
  KRW: '₩',
  BRL: 'R$',
  RUB: '₽',
  ZAR: 'R',
  AED: 'د.إ',
};

export const PLAN_QUERY_FILTER: Filter<Plan> = {
  fields: SOFT_DELETE_EXCLUDE,
  include: [
    {
      relation: 'planFeatures',
      scope: {
        fields: SOFT_DELETE_EXCLUDE,
        include: [
          {
            relation: 'feature',
            scope: { fields: SOFT_DELETE_EXCLUDE },
          },
        ],
      },
    },
  ],
};

export const TRIAL_PERIOD = 15;
export const TRIAL_PERIOD_UNIT = 'day';
