/* eslint-disable @typescript-eslint/no-explicit-any */
import { Fields, Filter, IncludeFilter, Order, Where } from '../types';

// Helper type for building filters
export class FilterBuilder<T = any> {
  private filter: Filter<T> = {};

  where(condition: Where<T>): this {
    this.filter.where = condition;
    return this;
  }

  limit(limit: number): this {
    this.filter.limit = limit;
    return this;
  }

  skip(skip: number): this {
    this.filter.skip = skip;
    return this;
  }

  offset(offset: number): this {
    this.filter.offset = offset;
    return this;
  }

  order(order: Order<T> | Order<T>[]): this {
    this.filter.order = order;
    return this;
  }

  fields(fields: Fields<T>): this {
    this.filter.fields = fields;
    return this;
  }

  include(include: IncludeFilter<T>[] | string[]): this {
    this.filter.include = include;
    return this;
  }

  build(): Filter<T> {
    return this.filter;
  }

  toJSON(): string {
    return JSON.stringify(this.filter);
  }
}

// Utility function to create filter
export const createFilter = <T = any>(): FilterBuilder<T> => {
  return new FilterBuilder<T>();
};

// Type guard to check if object is a valid filter
export const isValidFilter = <T = any>(obj: any): obj is Filter<T> => {
  if (typeof obj !== 'object' || obj === null) return false;

  const validKeys = [
    'where',
    'fields',
    'order',
    'limit',
    'skip',
    'offset',
    'include',
  ];
  const keys = Object.keys(obj);

  return keys.every((key) => validKeys.includes(key));
};

// Convert filter to query string
export const filterToQueryString = <T = any>(filter: Filter<T>): string => {
  const params = new URLSearchParams();
  params.append('filter', JSON.stringify(filter));
  return params.toString();
};
