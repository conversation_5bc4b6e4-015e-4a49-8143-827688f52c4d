/**
 * Audit Logger Service
 *
 * Centralized service for logging user activities and system events
 * to the audit log backend API.
 */

export interface AuditLogPayload {
  user: string;
  event: string;
  eventType:
    | 'auth'
    | 'user'
    | 'settings'
    | 'system'
    | 'chat'
    | 'bot'
    | 'alert'
    | 'navigation'
    | 'data_access';
  ipAddress: string;
  details: string;
  metadata?: Record<string, any>;
}

export interface AuditLoggerConfig {
  enabled: boolean;
  apiUrl: string;
  batchSize?: number;
  flushInterval?: number;
}

class AuditLoggerService {
  private config: AuditLoggerConfig;
  private queue: AuditLogPayload[] = [];
  private flushTimer: NodeJS.Timeout | null = null;
  private userEmail = '<EMAIL>';
  private ipAddress = '0.0.0.0';

  constructor() {
    this.config = {
      enabled: true,
      apiUrl:
        process.env.NEXT_PUBLIC_AUDIT_LOG_API_URL ||
        'http://127.0.0.1:3001/api/v1',
      batchSize: 10,
      flushInterval: 5000, // 5 seconds
    };

    // Get IP address on initialization
    this.fetchIPAddress();
  }

  /**
   * Fetch client IP address
   */
  private async fetchIPAddress(): Promise<void> {
    try {
      // Try to get IP from a public API
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      this.ipAddress = data.ip || '0.0.0.0';
    } catch (error) {
      // Fallback to localhost if fetch fails
      this.ipAddress = '127.0.0.1';
    }
  }

  /**
   * Set the current user email
   */
  public setUser(email: string): void {
    this.userEmail = email || '<EMAIL>';
  }

  /**
   * Get the current user email
   */
  public getUser(): string {
    return this.userEmail;
  }

  /**
   * Set IP address manually
   */
  public setIPAddress(ip: string): void {
    this.ipAddress = ip;
  }

  /**
   * Configure the audit logger
   */
  public configure(config: Partial<AuditLoggerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Log an audit event
   */
  public async log(
    event: string,
    eventType: AuditLogPayload['eventType'],
    details: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    const payload: AuditLogPayload = {
      user: this.userEmail,
      event,
      eventType,
      ipAddress: this.ipAddress,
      details,
      metadata: {
        ...metadata,
        timestamp: new Date().toISOString(),
        userAgent:
          typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
        url: typeof window !== 'undefined' ? window.location.href : 'unknown',
      },
    };

    // Add to queue
    this.queue.push(payload);

    // Flush if batch size reached
    if (this.queue.length >= (this.config.batchSize || 10)) {
      await this.flush();
    } else {
      // Schedule flush
      this.scheduleFlush();
    }
  }

  /**
   * Schedule automatic flush
   */
  private scheduleFlush(): void {
    if (this.flushTimer) {
      return;
    }

    this.flushTimer = setTimeout(() => {
      this.flush();
    }, this.config.flushInterval || 5000);
  }

  /**
   * Flush the queue to the backend
   */
  public async flush(): Promise<void> {
    if (this.flushTimer) {
      clearTimeout(this.flushTimer);
      this.flushTimer = null;
    }

    if (this.queue.length === 0) {
      return;
    }

    const logsToSend = [...this.queue];
    this.queue = [];

    try {
      // Send logs to backend
      await Promise.all(
        logsToSend.map((log) =>
          fetch(`${this.config.apiUrl}/audit-logs`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(log),
          })
        )
      );
    } catch (error) {
      console.error('Failed to send audit logs:', error);
      // Re-queue failed logs
      this.queue.unshift(...logsToSend);
    }
  }

  /**
   * Convenience methods for common event types
   */

  public logAuth(
    event: string,
    details: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    return this.log(event, 'auth', details, metadata);
  }

  public logNavigation(
    event: string,
    details: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    return this.log(event, 'navigation', details, metadata);
  }

  public logUserAction(
    event: string,
    details: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    return this.log(event, 'user', details, metadata);
  }

  public logSettings(
    event: string,
    details: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    return this.log(event, 'settings', details, metadata);
  }

  public logDataAccess(
    event: string,
    details: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    return this.log(event, 'data_access', details, metadata);
  }

  public logChat(
    event: string,
    details: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    return this.log(event, 'chat', details, metadata);
  }

  public logBot(
    event: string,
    details: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    return this.log(event, 'bot', details, metadata);
  }

  public logAlert(
    event: string,
    details: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    return this.log(event, 'alert', details, metadata);
  }

  public logSystem(
    event: string,
    details: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    return this.log(event, 'system', details, metadata);
  }

  /**
   * Cleanup on unmount
   */
  public async destroy(): Promise<void> {
    await this.flush();
    if (this.flushTimer) {
      clearTimeout(this.flushTimer);
      this.flushTimer = null;
    }
  }
}

// Singleton instance
export const auditLogger = new AuditLoggerService();

// Export for testing
export { AuditLoggerService };
