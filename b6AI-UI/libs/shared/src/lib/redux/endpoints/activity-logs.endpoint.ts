import { apiSlice } from '../api/apiSlice';
import { ApiTags } from '../../types/api-tags.type';

// Activity Log Types
export interface ActivityLog {
  id: string;
  timestamp: string;
  user: string;
  event: string;
  eventType: 'auth' | 'user' | 'settings' | 'system' | 'chat' | 'bot' | 'alert';
  ipAddress: string;
  details: string;
  metadata?: Record<string, any>;
}

export interface ActivityLogFilters {
  eventType?: string;
  user?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
}

export interface ActivityLogResponse {
  data: ActivityLog[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface ActivityLogRequest {
  page?: number;
  pageSize?: number;
  filters?: ActivityLogFilters;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// API Endpoints
export const activityLogsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get activity logs with pagination and filtering
    getActivityLogs: builder.query<
      ActivityLogResponse,
      ActivityLogRequest | void
    >({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();

        // Backend uses limit/offset instead of page/pageSize
        const limit = params.pageSize || 10;
        const offset = params.page ? (params.page - 1) * limit : 0;

        searchParams.append('limit', limit.toString());
        searchParams.append('offset', offset.toString());

        // Backend uses orderBy and orderDirection
        if (params.sortBy) searchParams.append('orderBy', params.sortBy);
        if (params.sortOrder) {
          searchParams.append('orderDirection', params.sortOrder.toUpperCase());
        }

        // Add filters directly (backend expects them as query params)
        if (params.filters) {
          if (params.filters.eventType)
            searchParams.append('eventType', params.filters.eventType);
          if (params.filters.user)
            searchParams.append('user', params.filters.user);
          if (params.filters.startDate)
            searchParams.append('startDate', params.filters.startDate);
          if (params.filters.endDate)
            searchParams.append('endDate', params.filters.endDate);
        }

        return {
          url: `/audit-logs?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      providesTags: [ApiTags.ACTIVITY_LOGS],
      transformResponse: (
        response: ActivityLog[],
        meta,
        arg
      ): ActivityLogResponse => {
        // Backend returns array directly, we need to calculate pagination
        const pageSize = arg?.pageSize || 10;
        const page = arg?.page || 1;
        const total = response.length;

        return {
          data: response,
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize),
        };
      },
    }),

    // Get a specific activity log by ID
    getActivityLogById: builder.query<ActivityLog, string>({
      query: (id) => `/audit-logs/${id}`,
      providesTags: (_result, _error, id) => [
        { type: ApiTags.ACTIVITY_LOGS, id },
      ],
    }),

    // Export activity logs
    exportActivityLogs: builder.mutation<Blob, ActivityLogRequest | void>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();

        if (params.filters) {
          Object.entries(params.filters).forEach(([key, value]) => {
            if (value) {
              searchParams.append(`filter.${key}`, value);
            }
          });
        }

        return {
          url: `/activity-logs/export?${searchParams.toString()}`,
          method: 'GET',
          responseHandler: 'blob',
        };
      },
    }),

    // Get activity log statistics
    getActivityLogStats: builder.query<
      {
        totalLogs: number;
        logsByType: Record<string, number>;
        logsByDate: Record<string, number>;
        topUsers: Array<{ user: string; count: number }>;
      },
      ActivityLogFilters | void
    >({
      query: (filters = {}) => {
        const searchParams = new URLSearchParams();

        Object.entries(filters).forEach(([key, value]) => {
          if (value) {
            searchParams.append(`filter.${key}`, value);
          }
        });

        return {
          url: `/activity-logs/stats?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      providesTags: [ApiTags.ACTIVITY_LOGS],
    }),
  }),
});

// Export hooks
export const {
  useGetActivityLogsQuery,
  useGetActivityLogByIdQuery,
  useExportActivityLogsMutation,
  useGetActivityLogStatsQuery,
  useLazyGetActivityLogsQuery,
} = activityLogsApi;

// Utility functions for working with activity logs
export const getEventTypeIcon = (type: ActivityLog['eventType']) => {
  switch (type) {
    case 'auth':
      return 'key';
    case 'user':
      return 'user';
    case 'settings':
      return 'settings';
    case 'system':
      return 'server';
    case 'chat':
      return 'message-square';
    case 'bot':
      return 'bot';
    case 'alert':
      return 'alert-triangle';
    default:
      return 'eye';
  }
};

export const getEventTypeColor = (type: ActivityLog['eventType']) => {
  switch (type) {
    case 'auth':
      return 'blue';
    case 'user':
      return 'green';
    case 'settings':
      return 'purple';
    case 'system':
      return 'gray';
    case 'chat':
      return 'cyan';
    case 'bot':
      return 'indigo';
    case 'alert':
      return 'red';
    default:
      return 'gray';
  }
};

export const formatActivityLogEvent = (log: ActivityLog) => {
  return {
    ...log,
    formattedTimestamp: new Date(log.timestamp).toLocaleString(),
    eventTypeIcon: getEventTypeIcon(log.eventType),
    eventTypeColor: getEventTypeColor(log.eventType),
  };
};
