import {
  ApiTags,
  BusinessHoursCreateDto,
  BusinessHoursResponseDto,
  Holiday,
  HolidayBulkCreateDto,
  OutOfOfficeSettings,
  OutOfOfficeSettingsDto,
  Department,
} from '../../types';
import { apiSlice } from '../api';

export const businessHoursApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Business Hours endpoints
    createBusinessHours: builder.mutation<BusinessHoursResponseDto, BusinessHoursCreateDto>({
      query: (businessHours) => ({
        url: '/business-hours',
        method: 'POST',
        body: businessHours,
      }),
      invalidatesTags: [ApiTags.BUSINESS_HOURS],
    }),

    getBusinessHoursByTenant: builder.query<BusinessHoursResponseDto[], string>({
      query: (tenantId) => `/business-hours/tenant/${tenantId}`,
      providesTags: [ApiTags.BUSINESS_HOURS],
    }),

    getBusinessHoursById: builder.query<BusinessHoursResponseDto, string>({
      query: (id) => `/business-hours/${id}`,
      providesTags: (_result, _error, id) => [
        { type: ApiTags.BUSINESS_HOURS, id },
      ],
    }),

    updateBusinessHours: builder.mutation<
      BusinessHoursResponseDto,
      { id: string; data: Partial<BusinessHoursCreateDto> }
    >({
      query: ({ id, data }) => ({
        url: `/business-hours/${id}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: ApiTags.BUSINESS_HOURS, id },
        ApiTags.BUSINESS_HOURS,
      ],
    }),

    deleteBusinessHours: builder.mutation<void, string>({
      query: (id) => ({
        url: `/business-hours/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (_result, _error, id) => [
        { type: ApiTags.BUSINESS_HOURS, id },
        ApiTags.BUSINESS_HOURS,
      ],
    }),

    // Holiday endpoints
    createHoliday: builder.mutation<Holiday, Omit<Holiday, 'id'>>({
      query: (holiday) => ({
        url: '/holidays',
        method: 'POST',
        body: holiday,
      }),
      invalidatesTags: [ApiTags.HOLIDAYS],
    }),

    createHolidaysBulk: builder.mutation<Holiday[], HolidayBulkCreateDto>({
      query: (bulkData) => ({
        url: '/holidays/bulk',
        method: 'POST',
        body: bulkData,
      }),
      invalidatesTags: [ApiTags.HOLIDAYS],
    }),

    getHolidaysByTenant: builder.query<
      Holiday[],
      { tenantId: string; year?: number; type?: string }
    >({
      query: ({ tenantId, year, type }) => {
        const params = new URLSearchParams();
        if (year) params.append('year', year.toString());
        if (type) params.append('type', type);
        return `/holidays/tenant/${tenantId}?${params.toString()}`;
      },
      providesTags: [ApiTags.HOLIDAYS],
    }),

    getUpcomingHolidays: builder.query<
      Holiday[],
      { tenantId: string; daysAhead?: number }
    >({
      query: ({ tenantId, daysAhead }) => {
        const params = new URLSearchParams();
        if (daysAhead) params.append('daysAhead', daysAhead.toString());
        return `/holidays/tenant/${tenantId}/upcoming?${params.toString()}`;
      },
      providesTags: [ApiTags.HOLIDAYS],
    }),

    updateHoliday: builder.mutation<
      Holiday,
      { id: string; data: Partial<Holiday> }
    >({
      query: ({ id, data }) => ({
        url: `/holidays/${id}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: ApiTags.HOLIDAYS, id },
        ApiTags.HOLIDAYS,
      ],
    }),

    deleteHoliday: builder.mutation<void, string>({
      query: (id) => ({
        url: `/holidays/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (_result, _error, id) => [
        { type: ApiTags.HOLIDAYS, id },
        ApiTags.HOLIDAYS,
      ],
    }),

    // Out of Office Settings endpoints
    createOrUpdateOutOfOfficeSettings: builder.mutation<OutOfOfficeSettings, OutOfOfficeSettingsDto>({
      query: (settings) => ({
        url: '/out-of-office-settings/upsert',
        method: 'POST',
        body: settings,
      }),
      invalidatesTags: [ApiTags.OUT_OF_OFFICE_SETTINGS],
    }),

    getOutOfOfficeSettingsByTenant: builder.query<OutOfOfficeSettings | null, string>({
      query: (tenantId) => `/out-of-office-settings/tenant/${tenantId}`,
      providesTags: [ApiTags.OUT_OF_OFFICE_SETTINGS],
    }),

    getActiveOutOfOfficeSettings: builder.query<OutOfOfficeSettings | null, string>({
      query: (tenantId) => `/out-of-office-settings/tenant/${tenantId}/active`,
      providesTags: [ApiTags.OUT_OF_OFFICE_SETTINGS],
    }),

    updateOutOfOfficeSettings: builder.mutation<
      OutOfOfficeSettings,
      { id: string; data: Partial<OutOfOfficeSettingsDto> }
    >({
      query: ({ id, data }) => ({
        url: `/out-of-office-settings/${id}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: ApiTags.OUT_OF_OFFICE_SETTINGS, id },
        ApiTags.OUT_OF_OFFICE_SETTINGS,
      ],
    }),

    deleteOutOfOfficeSettings: builder.mutation<void, string>({
      query: (id) => ({
        url: `/out-of-office-settings/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (_result, _error, id) => [
        { type: ApiTags.OUT_OF_OFFICE_SETTINGS, id },
        ApiTags.OUT_OF_OFFICE_SETTINGS,
      ],
    }),

    // Department endpoints
    getDepartments: builder.query<Department[], void>({
      query: () => '/departments',
      providesTags: [ApiTags.DEPARTMENTS],
    }),

    getActiveDepartments: builder.query<Department[], void>({
      query: () => '/departments/active',
      providesTags: [ApiTags.DEPARTMENTS],
    }),
  }),
});

export const {
  // Business Hours hooks
  useCreateBusinessHoursMutation,
  useGetBusinessHoursByTenantQuery,
  useGetBusinessHoursByIdQuery,
  useUpdateBusinessHoursMutation,
  useDeleteBusinessHoursMutation,
  
  // Holiday hooks
  useCreateHolidayMutation,
  useCreateHolidaysBulkMutation,
  useGetHolidaysByTenantQuery,
  useGetUpcomingHolidaysQuery,
  useUpdateHolidayMutation,
  useDeleteHolidayMutation,
  
  // Out of Office Settings hooks
  useCreateOrUpdateOutOfOfficeSettingsMutation,
  useGetOutOfOfficeSettingsByTenantQuery,
  useGetActiveOutOfOfficeSettingsQuery,
  useUpdateOutOfOfficeSettingsMutation,
  useDeleteOutOfOfficeSettingsMutation,
  
  // Department hooks
  useGetDepartmentsQuery,
  useGetActiveDepartmentsQuery,
} = businessHoursApi;
