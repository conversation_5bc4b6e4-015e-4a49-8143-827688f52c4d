import {
  ApiTags,
  CancelSubscriptionRequest,
  CheckoutDto,
  CheckoutResponseDto,
  CreateSubscriptionRequest,
  CreateSubscriptionResponse,
  Filter,
  ISubscription,
  Plan,
  UpdateSubscriptionRequest,
} from '../../types';
import { apiSlice } from '../api';

export const subscriptionApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all available plans
    getAllPlans: builder.query<Plan[], Filter<Plan> | void>({
      query: (filter) => ({
        url: '/plans',
        params: filter ? { filter: JSON.stringify(filter) } : undefined,
      }),
      providesTags: [ApiTags.SUBSCRIPTION_PLANS],
      transformResponse: (response: Plan[]) => {
        // Sort plans by price
        return response.sort((a, b) => a.priceCents - b.priceCents);
      },
    }),

    // Get a specific plan by ID
    getPlanById: builder.query<Plan, string>({
      query: (planId) => `/plans/${planId}`,
      providesTags: (_result, _error, planId) => [
        { type: ApiTags.SUBSCRIPTION_PLANS, id: planId },
      ],
    }),

    // Get current tenant's subscription
    getCurrentSubscription: builder.query<ISubscription | null, string | void>({
      query: (tenantId) => {
        if (tenantId) {
          return `/subscriptions/tenant/${tenantId}`;
        }
        return '/subscriptions/current';
      },
      providesTags: [ApiTags.SUBSCRIPTION, ApiTags.SUBSCRIPTION_STATUS],
    }),

    // Get subscription by ID
    getSubscriptionById: builder.query<ISubscription, string>({
      query: (subscriptionId) => `/subscriptions/${subscriptionId}`,
      providesTags: (_result, _error, id) => [
        { type: ApiTags.SUBSCRIPTION, id },
      ],
    }),

    // Create new subscription
    createSubscription: builder.mutation<
      CreateSubscriptionResponse,
      CreateSubscriptionRequest
    >({
      query: (subscriptionData) => ({
        url: '/subscriptions',
        method: 'POST',
        body: subscriptionData,
      }),
      invalidatesTags: [
        ApiTags.SUBSCRIPTION,
        ApiTags.SUBSCRIPTION_STATUS,
        ApiTags.USER,
      ],
    }),

    // Update subscription (change plan or status)
    updateSubscription: builder.mutation<
      ISubscription,
      {
        subscriptionId: string;
        data: UpdateSubscriptionRequest;
      }
    >({
      query: ({ subscriptionId, data }) => ({
        url: `/subscriptions/${subscriptionId}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (_result, _error, { subscriptionId }) => [
        { type: ApiTags.SUBSCRIPTION, id: subscriptionId },
        ApiTags.SUBSCRIPTION_STATUS,
      ],
    }),

    // Cancel subscription
    cancelSubscription: builder.mutation<
      ISubscription,
      {
        subscriptionId: string;
        data?: CancelSubscriptionRequest;
      }
    >({
      query: ({ subscriptionId, data }) => ({
        url: `/subscriptions/${subscriptionId}/cancel`,
        method: 'POST',
        body: data || {},
      }),
      invalidatesTags: (_result, _error, { subscriptionId }) => [
        { type: ApiTags.SUBSCRIPTION, id: subscriptionId },
        ApiTags.SUBSCRIPTION_STATUS,
      ],
    }),

    // Resume canceled subscription
    resumeSubscription: builder.mutation<ISubscription, string>({
      query: (subscriptionId) => ({
        url: `/subscriptions/${subscriptionId}/resume`,
        method: 'POST',
      }),
      invalidatesTags: (_result, _error, subscriptionId) => [
        { type: ApiTags.SUBSCRIPTION, id: subscriptionId },
        ApiTags.SUBSCRIPTION_STATUS,
      ],
    }),

    // Get subscription history
    getSubscriptionHistory: builder.query<ISubscription[], string>({
      query: (tenantId) => `/subscriptions/history/${tenantId}`,
      providesTags: [ApiTags.SUBSCRIPTION],
    }),

    // Checkout subscription
    checkoutSubscription: builder.mutation<CheckoutResponseDto, CheckoutDto>({
      query: (checkoutData) => ({
        url: '/subscriptions/checkout',
        method: 'POST',
        body: checkoutData,
      }),
      async onQueryStarted(_arg, { queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;
          if (data?.hostedPageUrl) {
            window.location.href = data.hostedPageUrl;
          }
        } catch {
          // Handle error if needed
        }
      },
      invalidatesTags: [ApiTags.SUBSCRIPTION, ApiTags.SUBSCRIPTION_STATUS],
    }),
  }),
});

export const {
  useGetAllPlansQuery,
  useGetPlanByIdQuery,
  useGetCurrentSubscriptionQuery,
  useGetSubscriptionByIdQuery,
  useCreateSubscriptionMutation,
  useUpdateSubscriptionMutation,
  useCancelSubscriptionMutation,
  useResumeSubscriptionMutation,
  useGetSubscriptionHistoryQuery,
  useCheckoutSubscriptionMutation,
} = subscriptionApi;
