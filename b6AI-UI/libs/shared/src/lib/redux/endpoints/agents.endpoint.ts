import { apiSlice } from '../api/apiSlice';
import { ApiTags } from '../../types/api-tags.type';

// Agent Types
export interface Agent {
  id: string;
  name: string;
  email: string;
  department: string;
  role: string;
  status: 'active' | 'inactive' | 'on leave';
  joinedDate: string;
  avatar?: string;
  phone?: string;
  lastLogin?: string;
  permissions?: string[];
  metadata?: Record<string, any>;
}

export interface AgentFilters {
  department?: string;
  status?: string;
  role?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
}

export interface AgentResponse {
  data: Agent[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface AgentRequest {
  page?: number;
  pageSize?: number;
  filters?: AgentFilters;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CreateAgentRequest {
  name: string;
  email: string;
  department: string;
  role: string;
  phone?: string;
  permissions?: string[];
}

export interface UpdateAgentRequest extends Partial<CreateAgentRequest> {
  id: string;
  status?: Agent['status'];
}

// API Endpoints
export const agentsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get agents with pagination and filtering
    getAgents: builder.query<AgentResponse, AgentRequest | void>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();
        
        if (params.page) searchParams.append('page', params.page.toString());
        if (params.pageSize) searchParams.append('pageSize', params.pageSize.toString());
        if (params.sortBy) searchParams.append('sortBy', params.sortBy);
        if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder);
        
        // Add filters
        if (params.filters) {
          Object.entries(params.filters).forEach(([key, value]) => {
            if (value) {
              searchParams.append(`filter.${key}`, value);
            }
          });
        }
        
        return {
          url: `/agents?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      providesTags: [ApiTags.AGENTS],
      transformResponse: (response: any): AgentResponse => {
        return {
          data: response.data || response.agents || response,
          total: response.total || response.data?.length || 0,
          page: response.page || 1,
          pageSize: response.pageSize || 10,
          totalPages: response.totalPages || Math.ceil((response.total || 0) / (response.pageSize || 10)),
        };
      },
    }),

    // Get a specific agent by ID
    getAgentById: builder.query<Agent, string>({
      query: (id) => `/agents/${id}`,
      providesTags: (_result, _error, id) => [
        { type: ApiTags.AGENTS, id },
      ],
    }),

    // Create a new agent
    createAgent: builder.mutation<Agent, CreateAgentRequest>({
      query: (agent) => ({
        url: '/agents',
        method: 'POST',
        body: agent,
      }),
      invalidatesTags: [ApiTags.AGENTS],
    }),

    // Update an existing agent
    updateAgent: builder.mutation<Agent, UpdateAgentRequest>({
      query: ({ id, ...agent }) => ({
        url: `/agents/${id}`,
        method: 'PUT',
        body: agent,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        ApiTags.AGENTS,
        { type: ApiTags.AGENTS, id },
      ],
    }),

    // Delete an agent
    deleteAgent: builder.mutation<void, string>({
      query: (id) => ({
        url: `/agents/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [ApiTags.AGENTS],
    }),

    // Bulk update agents
    bulkUpdateAgents: builder.mutation<void, {
      ids: string[];
      updates: Partial<Agent>;
    }>({
      query: ({ ids, updates }) => ({
        url: '/agents/bulk-update',
        method: 'PUT',
        body: { ids, updates },
      }),
      invalidatesTags: [ApiTags.AGENTS],
    }),

    // Export agents
    exportAgents: builder.mutation<Blob, AgentRequest | void>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();
        
        if (params.filters) {
          Object.entries(params.filters).forEach(([key, value]) => {
            if (value) {
              searchParams.append(`filter.${key}`, value);
            }
          });
        }
        
        return {
          url: `/agents/export?${searchParams.toString()}`,
          method: 'GET',
          responseHandler: 'blob',
        };
      },
    }),

    // Get agent statistics
    getAgentStats: builder.query<{
      totalAgents: number;
      activeAgents: number;
      agentsByDepartment: Record<string, number>;
      agentsByStatus: Record<string, number>;
      recentJoins: Agent[];
    }, AgentFilters | void>({
      query: (filters = {}) => {
        const searchParams = new URLSearchParams();
        
        Object.entries(filters).forEach(([key, value]) => {
          if (value) {
            searchParams.append(`filter.${key}`, value);
          }
        });
        
        return {
          url: `/agents/stats?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      providesTags: [ApiTags.AGENTS],
    }),

    // Get departments list
    getDepartments: builder.query<string[], void>({
      query: () => '/agents/departments',
      providesTags: [ApiTags.AGENTS],
    }),

    // Get roles list
    getRoles: builder.query<string[], void>({
      query: () => '/agents/roles',
      providesTags: [ApiTags.AGENTS],
    }),
  }),
});

// Export hooks
export const {
  useGetAgentsQuery,
  useGetAgentByIdQuery,
  useCreateAgentMutation,
  useUpdateAgentMutation,
  useDeleteAgentMutation,
  useBulkUpdateAgentsMutation,
  useExportAgentsMutation,
  useGetAgentStatsQuery,
  useGetDepartmentsQuery,
  useGetRolesQuery,
  useLazyGetAgentsQuery,
} = agentsApi;

// Utility functions for working with agents
export const getStatusVariant = (status: Agent['status']) => {
  switch (status) {
    case 'active':
      return 'success';
    case 'inactive':
      return 'error';
    case 'on leave':
      return 'warning';
    default:
      return 'default';
  }
};

export const formatAgentName = (agent: Agent) => {
  return agent.name;
};

export const getAgentInitials = (agent: Agent) => {
  return agent.name
    .split(' ')
    .map((n) => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

export const formatJoinDate = (joinedDate: string) => {
  return new Date(joinedDate).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};
