import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { ApiTags } from '../../types/api-tags.type';
import { RootState } from '../store';

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl:
      process.env.NEXT_PUBLIC_AUDIT_LOG_API_URL ||
      'http://127.0.0.1:3001/api/v1',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth?.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('authorization', `Bearer test-token`);
      return headers;
    },
  }),
  tagTypes: Object.values(ApiTags),
  endpoints: (builder) => ({}),
});
