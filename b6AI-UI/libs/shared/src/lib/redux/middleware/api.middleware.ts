import { isRejectedWithValue } from '@reduxjs/toolkit';
import type { MiddlewareAPI, Middleware } from '@reduxjs/toolkit';

export const apiErrorMiddleware: Middleware =
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (api: MiddlewareAPI) => (next) => (action: any) => {
    if (isRejectedWithValue(action)) {
      console.error('API Error:', action.payload);

      // Handle specific error cases
      if (action.payload?.status === 401) {
        // Handle unauthorized
        api.dispatch({ type: 'auth/logout' });
      } else if (action.payload?.status === 403) {
        // Handle forbidden
        console.error('Forbidden access');
      }
    }

    return next(action);
  };
