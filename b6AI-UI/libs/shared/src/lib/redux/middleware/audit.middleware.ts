/**
 * Audit Logging Middleware for Redux
 * 
 * Automatically logs important Redux actions to the audit log system
 */

import type { Middleware } from '@reduxjs/toolkit';
import { auditLogger } from '../../services/audit-logger.service';

// Actions that should be logged
const LOGGABLE_ACTIONS = {
  // Auth actions
  'auth/loginSuccess': {
    event: 'User Login',
    eventType: 'auth' as const,
    getDetails: (action: any) => `User ${action.payload?.user?.email || 'unknown'} logged in successfully`,
  },
  'auth/logout': {
    event: 'User Logout',
    eventType: 'auth' as const,
    getDetails: () => 'User logged out',
  },
  'auth/loginFailure': {
    event: 'Login Failed',
    eventType: 'auth' as const,
    getDetails: () => 'Login attempt failed',
  },

  // UI actions
  'ui/toggleSidebar': {
    event: 'Sidebar Toggled',
    eventType: 'user' as const,
    getDetails: (action: any) => `Sidebar ${action.payload ? 'opened' : 'closed'}`,
  },
  'ui/setTheme': {
    event: 'Theme Changed',
    eventType: 'settings' as const,
    getDetails: (action: any) => `Theme changed to ${action.payload}`,
  },

  // API actions (RTK Query)
  'api/executeMutation/fulfilled': {
    event: 'Data Modified',
    eventType: 'data_access' as const,
    getDetails: (action: any) => {
      const endpoint = action.meta?.arg?.endpointName || 'unknown';
      return `Data modification via ${endpoint}`;
    },
  },
};

// Actions that should NOT be logged (to avoid noise)
const IGNORED_ACTIONS = [
  'api/executeQuery/pending',
  'api/executeQuery/fulfilled',
  'api/executeQuery/rejected',
  'api/config/middlewareRegistered',
  'logger/', // Ignore logger actions
];

/**
 * Check if an action should be ignored
 */
function shouldIgnoreAction(actionType: string): boolean {
  return IGNORED_ACTIONS.some((pattern) => actionType.startsWith(pattern));
}

/**
 * Audit logging middleware
 */
export const auditMiddleware: Middleware = (store) => (next) => (action: any) => {
  // Skip if not enabled or action is ignored
  if (shouldIgnoreAction(action.type)) {
    return next(action);
  }

  // Get current state before action
  const prevState = store.getState();

  // Execute action
  const result = next(action);

  // Get new state after action
  const nextState = store.getState();

  // Check if this action should be logged
  const logConfig = LOGGABLE_ACTIONS[action.type as keyof typeof LOGGABLE_ACTIONS];

  if (logConfig) {
    const details = logConfig.getDetails(action);
    const metadata = {
      action: action.type,
      payload: action.payload,
      prevState: {
        auth: prevState.auth?.isAuthenticated,
        ui: prevState.ui?.theme,
      },
      nextState: {
        auth: nextState.auth?.isAuthenticated,
        ui: nextState.ui?.theme,
      },
    };

    // Log asynchronously to avoid blocking
    auditLogger.log(logConfig.event, logConfig.eventType, details, metadata).catch((error) => {
      console.error('Failed to log audit event:', error);
    });
  }

  return result;
};

