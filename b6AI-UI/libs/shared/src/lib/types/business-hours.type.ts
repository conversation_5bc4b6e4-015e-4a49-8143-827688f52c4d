// Business Hours Types
export interface BusinessHours {
  id?: string;
  tenantId: string;
  name: string;
  timezone: string;
  workingDays: number[];
  workingHours: object;
  lunchBreak?: object;
  isDefault?: boolean;
  status?: string;
  createdOn?: string;
  modifiedOn?: string;
}

export interface BusinessHoursCreateDto {
  tenantId: string;
  name: string;
  timezone: string;
  workingDays: number[];
  workingHours: object;
  lunchBreak?: object;
  isDefault?: boolean;
  departmentIds: string[];
  status?: string;
}

export interface BusinessHoursResponseDto {
  id?: string;
  tenantId: string;
  name: string;
  timezone: string;
  workingDays: number[];
  workingHours: object;
  lunchBreak?: object;
  isDefault?: boolean;
  status?: string;
  departments?: Department[];
  createdOn?: string;
  modifiedOn?: string;
}

// Holiday Types
export interface Holiday {
  id?: string;
  tenantId: string;
  name: string;
  date: Date;
  type?: string;
  countryCode?: string;
  status?: string;
  createdOn?: string;
  modifiedOn?: string;
}

export interface HolidayBulkCreateDto {
  tenantId: string;
  holidays: Array<{
    name: string;
    date: Date;
    type?: string;
    countryCode?: string;
  }>;
}

// Out of Office Settings Types
export interface OutOfOfficeSettings {
  id?: string;
  tenantId: string;
  afterHoursMessage?: string;
  holidayMessage?: string;
  collectContactInfo?: boolean;
  emergencyContactInfo?: object;
  status?: string;
  createdOn?: string;
  modifiedOn?: string;
}

export interface OutOfOfficeSettingsDto {
  tenantId: string;
  afterHoursMessage?: string;
  holidayMessage?: string;
  collectContactInfo?: boolean;
  emergencyContactInfo?: object;
  status?: string;
}

// Department Types
export interface Department {
  id?: string;
  name: string;
  description?: string;
  status?: string;
  createdOn?: string;
  modifiedOn?: string;
}
