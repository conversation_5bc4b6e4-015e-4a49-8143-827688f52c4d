import { SoftDelete } from './base.type';
import { Plan } from './plan.type';

export enum SubscriptionStatus {
  IN_TRIAL = 'in_trial',
  ACTIVE = 'active',
  NON_RENEWING = 'non_renewing',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
  FUTURE = 'future', // not started yet
}

export enum PERIOD_UNIT {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year',
}

export enum BILLING_PERIOD {
  MONTHLY = 'Monthly',
  YEARLY = 'yearly',
}

export interface ISubscription extends SoftDelete {
  id: string;
  tenantId: string;
  status: string;
  startDate: Date;
  endDate?: Date;
  trialEndDate?: Date;
  renewalDate?: Date;
  gatewayName: string;
  gatewayRefId: string;
  planId: string;
  plan?: Plan;
  billingPeriod: BILLING_PERIOD;
}

export interface SubscriptionState {
  currentSubscription: ISubscription | null;
  selectedPlanId: string | null;
  billingPeriod: BILLING_PERIOD;
  isLoading: boolean;
  error: string | null;
}

export interface CreateSubscriptionRequest {
  planId: string;
  tenantId?: string; // Optional if backend gets it from auth context
  paymentMethodId?: string; // Stripe payment method ID
  billingPeriod?: BILLING_PERIOD;
  couponCode?: string;
  startTrial?: boolean;
}

export interface CreateSubscriptionResponse {
  subscription: ISubscription;
  clientSecret?: string; // For 3D Secure authentication if needed
  requiresAction?: boolean;
}

export interface UpdateSubscriptionRequest {
  planId?: string;
  status?: SubscriptionStatus;
}

export interface CancelSubscriptionRequest {
  immediately?: boolean;
  reason?: string;
}

export interface BillingCustomer {
  id?: string;
  firstName: string;
  lastName: string;
  email: string;
  company?: string;
  tenantId: string;
  gatewayName?: string;
  gatewayRefId?: string;
}

export interface CheckoutDto extends BillingCustomer {
  planId: string;
  period: string;
}

export interface CheckoutResponseDto {
  hostedPageUrl: string;
}
