export enum ApiTags {
  // User related
  USER = 'User',
  USER_PROFILE = 'UserProfile',
  USER_SETTINGS = 'UserSettings',

  // Subscription related
  SUBSCRIPTION = 'Subscription',
  SUBSCRIPTION_PLANS = 'SubscriptionPlans',
  SUBSCRIPTION_STATUS = 'SubscriptionStatus',

  // Onboarding related
  ONBOARD = 'Onboard',
  ONBOARD_STEPS = 'OnboardSteps',
  ONBOARD_PROGRESS = 'OnboardProgress',

  // Agent related
  AGENTS = 'Agents',
  AGENT_PROFILE = 'AgentProfile',
  AGENT_PERMISSIONS = 'AgentPermissions',

  // Activity logs
  ACTIVITY_LOGS = 'ActivityLogs',
  ACTIVITY_LOG_STATS = 'ActivityLogStats',
  // Business Hours related
  BUSINESS_HOURS = 'BusinessHours',
  HOLIDAYS = 'Holidays',
  OUT_OF_OFFICE_SETTINGS = 'OutOfOfficeSettings',
  DEPARTMENTS = 'Departments',

  // Other entities
  NOTIFICATIONS = 'Notifications',
  DASHBOARD = 'Dashboard',
  ANALYTICS = 'Analytics',
}
