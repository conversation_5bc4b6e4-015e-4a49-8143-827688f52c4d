/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * LoopBack 4 Filter types
 * Based on @loopback/filter package
 */

// Comparison operators
export type Operators =
  | 'eq' // Equal
  | 'neq' // Not equal
  | 'gt' // Greater than
  | 'gte' // Greater than or equal
  | 'lt' // Less than
  | 'lte' // Less than or equal
  | 'inq' // In array
  | 'nin' // Not in array
  | 'between' // Between two values
  | 'exists' // Property exists
  | 'and' // Logical AND
  | 'or' // Logical OR
  | 'like' // LIKE operator (use it with regexp)
  | 'nlike' // NOT LIKE operator
  | 'ilike' // Case-insensitive LIKE
  | 'nilike' // Case-insensitive NOT LIKE
  | 'regexp'; // Regular expression

// Where clause condition
export type WhereCondition<T> = {
  [K in keyof T]?:
    | T[K]
    | {
        [op in Operators]?: T[K] | T[K][] | [T[K], T[K]];
      };
} & {
  and?: Where<T>[];
  or?: Where<T>[];
};

export type Where<T> = WhereCondition<T>;

// Fields projection
export type Fields<T> = {
  [K in keyof T]?: boolean | 0 | 1;
};

// Order by
export type Order<T> =
  | `${Extract<keyof T, string>} ${'ASC' | 'DESC' | 'asc' | 'desc'}`
  | `${Extract<keyof T, string>}`;

// Include filter for relations
export interface IncludeFilter<T = any> {
  relation: string;
  scope?: Filter<T>;
}

// Main Filter interface
export interface Filter<T = any> {
  where?: Where<T>;
  fields?: Fields<T>;
  order?: Order<T> | Order<T>[];
  limit?: number;
  skip?: number;
  offset?: number;
  include?: IncludeFilter<T>[] | string[];
}

// Filter excluding where clause (for sub-queries)
export type FilterExcludingWhere<T = any> = Omit<Filter<T>, 'where'>;

// Count filter
export interface CountFilter<T = any> {
  where?: Where<T>;
}

// Specific filter types for common use cases
export interface PaginationFilter {
  limit?: number;
  skip?: number;
  offset?: number;
}

export interface SortFilter<T = any> {
  order?: Order<T> | Order<T>[];
}

export interface ProjectionFilter<T = any> {
  fields?: Fields<T>;
}
