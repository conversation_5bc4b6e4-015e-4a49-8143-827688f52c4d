'use client';

/**
 * React Hook for Audit Logging
 *
 * Provides easy access to audit logging functionality in React components
 */

import { useCallback, useEffect } from 'react';
import { auditLogger, AuditLogPayload } from '../services/audit-logger.service';

export interface UseAuditLoggerReturn {
  log: (
    event: string,
    eventType: AuditLogPayload['eventType'],
    details: string,
    metadata?: Record<string, any>
  ) => Promise<void>;
  logAuth: (
    event: string,
    details: string,
    metadata?: Record<string, any>
  ) => Promise<void>;
  logNavigation: (
    event: string,
    details: string,
    metadata?: Record<string, any>
  ) => Promise<void>;
  logUserAction: (
    event: string,
    details: string,
    metadata?: Record<string, any>
  ) => Promise<void>;
  logSettings: (
    event: string,
    details: string,
    metadata?: Record<string, any>
  ) => Promise<void>;
  logDataAccess: (
    event: string,
    details: string,
    metadata?: Record<string, any>
  ) => Promise<void>;
  logChat: (
    event: string,
    details: string,
    metadata?: Record<string, any>
  ) => Promise<void>;
  logBot: (
    event: string,
    details: string,
    metadata?: Record<string, any>
  ) => Promise<void>;
  logAlert: (
    event: string,
    details: string,
    metadata?: Record<string, any>
  ) => Promise<void>;
  logSystem: (
    event: string,
    details: string,
    metadata?: Record<string, any>
  ) => Promise<void>;
  setUser: (email: string) => void;
}

/**
 * Hook to use audit logger in React components
 */
export function useAuditLogger(): UseAuditLoggerReturn {
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      auditLogger.flush();
    };
  }, []);

  const log = useCallback(
    async (
      event: string,
      eventType: AuditLogPayload['eventType'],
      details: string,
      metadata?: Record<string, any>
    ) => {
      await auditLogger.log(event, eventType, details, metadata);
    },
    []
  );

  const logAuth = useCallback(
    async (event: string, details: string, metadata?: Record<string, any>) => {
      await auditLogger.logAuth(event, details, metadata);
    },
    []
  );

  const logNavigation = useCallback(
    async (event: string, details: string, metadata?: Record<string, any>) => {
      await auditLogger.logNavigation(event, details, metadata);
    },
    []
  );

  const logUserAction = useCallback(
    async (event: string, details: string, metadata?: Record<string, any>) => {
      await auditLogger.logUserAction(event, details, metadata);
    },
    []
  );

  const logSettings = useCallback(
    async (event: string, details: string, metadata?: Record<string, any>) => {
      await auditLogger.logSettings(event, details, metadata);
    },
    []
  );

  const logDataAccess = useCallback(
    async (event: string, details: string, metadata?: Record<string, any>) => {
      await auditLogger.logDataAccess(event, details, metadata);
    },
    []
  );

  const logChat = useCallback(
    async (event: string, details: string, metadata?: Record<string, any>) => {
      await auditLogger.logChat(event, details, metadata);
    },
    []
  );

  const logBot = useCallback(
    async (event: string, details: string, metadata?: Record<string, any>) => {
      await auditLogger.logBot(event, details, metadata);
    },
    []
  );

  const logAlert = useCallback(
    async (event: string, details: string, metadata?: Record<string, any>) => {
      await auditLogger.logAlert(event, details, metadata);
    },
    []
  );

  const logSystem = useCallback(
    async (event: string, details: string, metadata?: Record<string, any>) => {
      await auditLogger.logSystem(event, details, metadata);
    },
    []
  );

  const setUser = useCallback((email: string) => {
    auditLogger.setUser(email);
  }, []);

  return {
    log,
    logAuth,
    logNavigation,
    logUserAction,
    logSettings,
    logDataAccess,
    logChat,
    logBot,
    logAlert,
    logSystem,
    setUser,
  };
}
