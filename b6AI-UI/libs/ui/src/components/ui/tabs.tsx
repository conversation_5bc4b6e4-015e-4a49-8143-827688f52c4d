import React, { useEffect, useState } from 'react';
interface TabItem {
  id: string;
  label: string;
  content: React.ReactNode;
}
interface TabsProps {
  items: TabItem[];
  defaultTab?: string;
  onTabChange?: (tabId: string) => void;
}
export const Tabs: React.FC<TabsProps> = ({
  items,
  defaultTab,
  onTabChange,
}) => {
  const [activeTab, setActiveTab] = useState<string | undefined>(
    defaultTab || items[0]?.id
  );
  useEffect(() => {
    if (!items.length) {
      if (activeTab !== undefined) {
        setActiveTab(undefined);
      }
      return;
    }

    const currentIsValid =
      activeTab && items.some((item) => item.id === activeTab);

    let nextActive = activeTab;

    if (defaultTab && items.some((item) => item.id === defaultTab)) {
      nextActive = currentIsValid ? activeTab : defaultTab;
    } else if (!currentIsValid) {
      nextActive = items[0].id;
    }

    if (nextActive !== activeTab) {
      setActiveTab(nextActive);
      if (nextActive && onTabChange) {
        onTabChange(nextActive);
      }
    }
  }, [activeTab, defaultTab, items, onTabChange]);
  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
    if (onTabChange) {
      onTabChange(tabId);
    }
  };
  const activeTabContent = items.find((item) => item.id === activeTab)?.content;
  return (
    <div>
      <div className="flex border-b border-light-border dark:border-dark-border overflow-x-auto">
        {items.map((tab) => (
          <button
            key={tab.id}
            className={`px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === tab.id
                ? 'border-b-2 border-b6ai-blue dark:border-b6ai-cyan text-b6ai-blue dark:text-b6ai-cyan'
                : 'text-light-muted-foreground dark:text-dark-muted-foreground hover:text-light-foreground dark:hover:text-dark-foreground'
            }`}
            onClick={() => handleTabClick(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </div>
      <div>{activeTabContent}</div>
    </div>
  );
};
