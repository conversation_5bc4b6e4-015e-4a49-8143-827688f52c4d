'use client';
import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { CheckCircle, XCircle, AlertCircle, Info, X } from 'lucide-react';

export type ToastType =
  | 'success'
  | 'error'
  | 'warning'
  | 'info'
  | 'gradient'
  | 'custom';

export interface ToastItem {
  id: string;
  type: ToastType;
  message: string;
  duration?: number;
  customClass?: string;
}

interface CustomToastProps {
  type: ToastType;
  message: string;
  onClose: () => void;
  customClass?: string;
}

const CustomToast: React.FC<CustomToastProps> = ({
  type,
  message,
  onClose,
  customClass,
}) => {
  const getToastStyles = () => {
    const baseStyles =
      'flex items-center gap-3 p-4 rounded-lg shadow-lg min-w-[300px] max-w-[500px] animate-slide-in';

    if (customClass) return `${baseStyles} ${customClass}`;

    switch (type) {
      case 'success':
        return `${baseStyles} bg-green-500 text-white`;
      case 'error':
        return `${baseStyles} bg-red-500 text-white`;
      case 'warning':
        return `${baseStyles} bg-yellow-500 text-white`;
      case 'info':
        return `${baseStyles} bg-blue-500 text-white`;
      case 'gradient':
        return `${baseStyles} bg-gradient-to-r from-purple-500 to-pink-500 text-white`;
      default:
        return `${baseStyles} bg-gray-800 text-white`;
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5" />;
      case 'error':
        return <XCircle className="w-5 h-5" />;
      case 'warning':
        return <AlertCircle className="w-5 h-5" />;
      case 'info':
        return <Info className="w-5 h-5" />;
      default:
        return null;
    }
  };

  return (
    <div className={getToastStyles()}>
      {getIcon()}
      <span className="flex-1">{message}</span>
      <button
        onClick={onClose}
        className="hover:opacity-70 transition-opacity"
        aria-label="Close"
      >
        <X className="w-4 h-4" />
      </button>
    </div>
  );
};

interface ToastContainerProps {
  toasts: ToastItem[];
  removeToast: (id: string) => void;
}
export const ToastContainer: React.FC<ToastContainerProps> = ({
  toasts,
  removeToast,
}) => {
  const [portalElement, setPortalElement] = useState<HTMLElement | null>(null);
  useEffect(() => {
    let element = document.getElementById('toast-container');
    if (!element) {
      element = document.createElement('div');
      element.id = 'toast-container';
      element.className = 'fixed top-4 right-4 z-50 flex flex-col gap-2';
      document.body.appendChild(element);
    }
    setPortalElement(element);
    return () => {
      if (element && element.parentElement) {
        element.parentElement.removeChild(element);
      }
    };
  }, []);
  if (!portalElement) return null;
  return createPortal(
    <>
      {toasts.map((toast) => (
        <div key={toast.id} className="animate-fade-in">
          <CustomToast
            type={toast.type}
            message={toast.message}
            onClose={() => removeToast(toast.id)}
            customClass={toast.customClass}
          />
        </div>
      ))}
    </>,
    portalElement
  );
};
