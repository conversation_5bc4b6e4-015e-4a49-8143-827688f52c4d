import { cn } from '../../lib/utils';

export type LoaderProps = {
  fixed?: boolean;
  height?: number;
  className?: string;
  trackClassName?: string;
  barClassName?: string;
};

/**
 * Loader
 * Top progress bar with brand gradient and indeterminate animation
 * Uses shadcn theme tokens and centralized Tailwind CSS variables.
 */
export function Loader({
  fixed = true,
  height = 4,
  className,
  trackClassName,
  barClassName,
}: LoaderProps) {
  return (
    <div
      className={cn(
        fixed && 'fixed left-0 top-0 z-[2001] w-full',
        !fixed && 'w-full',
        className
      )}
    >
      <div
        className={cn(
          'relative w-full overflow-hidden rounded-none bg-muted',
          trackClassName
        )}
        style={{ height }}
      >
        {/* Indeterminate bars */}
        <span
          className={cn(
            'absolute inset-y-0 will-change-[left,right] bg-b6ai-gradient-animated',
            'animate-loader-indeterminate1',
            barClassName
          )}
          aria-hidden="true"
        />
        <span
          className={cn(
            'absolute inset-y-0 will-change-[left,right] bg-b6ai-gradient-animated',
            'animate-loader-indeterminate2',
            barClassName
          )}
          aria-hidden="true"
        />
      </div>
    </div>
  );
}

export default Loader;
