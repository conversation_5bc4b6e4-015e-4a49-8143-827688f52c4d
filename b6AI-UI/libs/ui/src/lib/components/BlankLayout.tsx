import React from 'react';

type BlankLayoutProps = React.PropsWithChildren<{
  className?: string;
  center?: boolean; // vertically center content
}>;

/**
 * BlankLayout
 * - Full-page layout (min-h-screen) that uses shadcn-style theming variables
 * - Responsive horizontal padding (small -> large breakpoints)
 * - Keeps markup minimal so it works for Next.js _app.tsx or pages
 *
 * Assumptions:
 * - Your tailwind config maps tokens like `background`/`foreground` to CSS vars
 *   (example: `bg-background` -> `background-color: hsl(var(--background))`)
 * - You control theme by toggling a `.dark` class on <html> or body (shadcn pattern)
 */

export function BlankLayout({
  children,
  className = '',
  center = false,
}: BlankLayoutProps) {
  return (
    <div
      className={`min-h-screen w-full bg-background text-foreground antialiased ${
        center ? 'flex items-center' : ''
      }`}
      // let parent/theme control color-scheme via CSS vars like --background / --foreground
    >
      {/* Horizontal padding responsive: small on mobile, grows on larger screens */}
      <div className={`w-full  ${className}`}>{children}</div>
    </div>
  );
}

/**
 * Example usage in Next.js (_app.tsx):
 *
 * import BlankLayout from '@/layouts/BlankLayout';
 *
 * export default function App({ Component, pageProps }) {
 *   return (
 *     <BlankLayout>
 *       <Component {...pageProps} />
 *     </BlankLayout>
 *   );
 * }
 */
