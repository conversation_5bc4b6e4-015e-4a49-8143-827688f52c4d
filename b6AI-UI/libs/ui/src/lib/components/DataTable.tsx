import React, { useState, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  flexRender,
  ColumnDef,
  SortingState,
  ColumnFiltersState,
  VisibilityState,
  PaginationState,
} from '@tanstack/react-table';
import {
  ChevronDownIcon,
  ChevronUpIcon,
  ChevronsUpDownIcon,
  SearchIcon,
  RefreshCwIcon,
  DownloadIcon,
} from 'lucide-react';
import { cn } from '../utils';

export interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  searchPlaceholder?: string;
  enableSearch?: boolean;
  enableFilters?: boolean;
  enablePagination?: boolean;
  enableSorting?: boolean;
  enableColumnVisibility?: boolean;
  pageSize?: number;
  pageSizeOptions?: number[];
  onRefresh?: () => void;
  onExport?: () => void;
  isLoading?: boolean;
  className?: string;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  filters?: ColumnFiltersState;
  onFiltersChange?: (filters: ColumnFiltersState) => void;
  customFilters?: React.ReactNode;
  emptyMessage?: string;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  searchPlaceholder = 'Search...',
  enableSearch = true,
  enableFilters = true,
  enablePagination = true,
  enableSorting = true,
  enableColumnVisibility = false,
  pageSize = 10,
  pageSizeOptions = [10, 20, 30, 40, 50],
  onRefresh,
  onExport,
  isLoading = false,
  className,
  searchValue: externalSearchValue,
  onSearchChange: externalOnSearchChange,
  filters: externalFilters,
  onFiltersChange: externalOnFiltersChange,
  customFilters,
  emptyMessage = 'No results found.',
}: DataTableProps<TData, TValue>) {
  // Internal state for search and filters when not controlled externally
  const [internalSearchValue, setInternalSearchValue] = useState('');
  const [internalFilters, setInternalFilters] = useState<ColumnFiltersState>(
    []
  );

  // Use external values if provided, otherwise use internal state
  const searchValue =
    externalSearchValue !== undefined
      ? externalSearchValue
      : internalSearchValue;
  const onSearchChange = externalOnSearchChange || setInternalSearchValue;
  const filters =
    externalFilters !== undefined ? externalFilters : internalFilters;
  const onFiltersChange = externalOnFiltersChange || setInternalFilters;

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize,
  });

  // Global filter function for search
  const globalFilter = useMemo(() => {
    if (!enableSearch || !searchValue) return undefined;
    return searchValue;
  }, [enableSearch, searchValue]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: enableSorting ? getSortedRowModel() : undefined,
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: enablePagination
      ? getPaginationRowModel()
      : undefined,
    onSortingChange: enableSorting ? setSorting : undefined,
    onColumnFiltersChange: onFiltersChange
      ? (updaterOrValue) => {
          const newFilters =
            typeof updaterOrValue === 'function'
              ? updaterOrValue(filters)
              : updaterOrValue;
          onFiltersChange(newFilters);
        }
      : undefined,
    onColumnVisibilityChange: enableColumnVisibility
      ? setColumnVisibility
      : undefined,
    onPaginationChange: enablePagination ? setPagination : undefined,
    onGlobalFilterChange: onSearchChange,
    globalFilterFn: 'includesString',
    state: {
      sorting: enableSorting ? sorting : undefined,
      columnFilters: filters,
      columnVisibility: enableColumnVisibility ? columnVisibility : undefined,
      pagination: enablePagination ? pagination : undefined,
      globalFilter: globalFilter,
    },
  });

  return (
    <div className={cn('w-full space-y-4', className)}>
      {/* Header with search, filters, and actions */}
      {(enableSearch ||
        enableFilters ||
        onRefresh ||
        onExport ||
        customFilters) && (
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div className="flex flex-col md:flex-row gap-4 flex-1">
            {/* Search */}
            {enableSearch && (
              <div className="relative flex-1 max-w-sm">
                <SearchIcon
                  size={16}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-light-muted-foreground dark:text-dark-muted-foreground"
                />
                <input
                  type="text"
                  placeholder={searchPlaceholder}
                  value={searchValue}
                  onChange={(e) => onSearchChange(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                />
              </div>
            )}

            {/* Custom Filters */}
            {customFilters && (
              <div className="flex items-center gap-2">{customFilters}</div>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            {onRefresh && (
              <button
                onClick={onRefresh}
                disabled={isLoading}
                className="p-2 text-light-muted-foreground dark:text-dark-muted-foreground hover:text-light-foreground dark:hover:text-dark-foreground rounded hover:bg-light-muted dark:hover:bg-dark-muted disabled:opacity-50"
              >
                <RefreshCwIcon
                  size={16}
                  className={isLoading ? 'animate-spin' : ''}
                />
              </button>
            )}
            {onExport && (
              <button
                onClick={onExport}
                className="p-2 text-light-muted-foreground dark:text-dark-muted-foreground hover:text-light-foreground dark:hover:text-dark-foreground rounded hover:bg-light-muted dark:hover:bg-dark-muted"
              >
                <DownloadIcon size={16} />
              </button>
            )}
          </div>
        </div>
      )}

      {/* Table */}
      <div className="bg-light-card dark:bg-dark-card rounded-lg border border-light-border dark:border-dark-border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead className="text-xs uppercase bg-light-muted dark:bg-dark-muted">
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <th
                      key={header.id}
                      className={cn(
                        'px-4 py-3 text-left',
                        header.column.getCanSort() && enableSorting
                          ? 'cursor-pointer select-none hover:bg-light-muted/50 dark:hover:bg-dark-muted/50'
                          : ''
                      )}
                      onClick={header.column.getToggleSortingHandler()}
                    >
                      <div className="flex items-center">
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                        {header.column.getCanSort() && enableSorting && (
                          <span className="ml-2">
                            {header.column.getIsSorted() === 'desc' ? (
                              <ChevronDownIcon size={14} />
                            ) : header.column.getIsSorted() === 'asc' ? (
                              <ChevronUpIcon size={14} />
                            ) : (
                              <ChevronsUpDownIcon size={14} />
                            )}
                          </span>
                        )}
                      </div>
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              {isLoading ? (
                <tr>
                  <td
                    colSpan={columns.length}
                    className="px-4 py-8 text-center text-light-muted-foreground dark:text-dark-muted-foreground"
                  >
                    <div className="flex items-center justify-center">
                      <RefreshCwIcon size={16} className="animate-spin mr-2" />
                      Loading...
                    </div>
                  </td>
                </tr>
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <tr
                    key={row.id}
                    className="border-b border-light-border dark:border-dark-border hover:bg-light-muted/30 dark:hover:bg-dark-muted/30"
                  >
                    {row.getVisibleCells().map((cell) => (
                      <td key={cell.id} className="px-4 py-3">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={columns.length}
                    className="px-4 py-8 text-center text-light-muted-foreground dark:text-dark-muted-foreground"
                  >
                    {emptyMessage}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {enablePagination &&
          !isLoading &&
          table.getRowModel().rows?.length > 0 && (
            <div className="flex items-center justify-between px-4 py-3 border-t border-light-border dark:border-dark-border">
              <div className="text-sm text-light-muted-foreground dark:text-dark-muted-foreground">
                Showing{' '}
                <span className="font-medium">
                  {table.getState().pagination.pageIndex *
                    table.getState().pagination.pageSize +
                    1}
                </span>{' '}
                to{' '}
                <span className="font-medium">
                  {Math.min(
                    (table.getState().pagination.pageIndex + 1) *
                      table.getState().pagination.pageSize,
                    table.getFilteredRowModel().rows.length
                  )}
                </span>{' '}
                of{' '}
                <span className="font-medium">
                  {table.getFilteredRowModel().rows.length}
                </span>{' '}
                results
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => table.previousPage()}
                  disabled={!table.getCanPreviousPage()}
                  className="px-3 py-1 text-sm border border-light-border dark:border-dark-border rounded hover:bg-light-muted dark:hover:bg-dark-muted disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>

                {/* Page numbers */}
                {Array.from(
                  { length: Math.min(5, table.getPageCount()) },
                  (_, i) => {
                    const pageIndex = table.getState().pagination.pageIndex;
                    const pageCount = table.getPageCount();
                    let pageNumber;

                    if (pageCount <= 5) {
                      pageNumber = i;
                    } else if (pageIndex < 3) {
                      pageNumber = i;
                    } else if (pageIndex > pageCount - 4) {
                      pageNumber = pageCount - 5 + i;
                    } else {
                      pageNumber = pageIndex - 2 + i;
                    }

                    return (
                      <button
                        key={pageNumber}
                        onClick={() => table.setPageIndex(pageNumber)}
                        className={cn(
                          'px-3 py-1 text-sm rounded',
                          pageNumber === pageIndex
                            ? 'bg-b6ai-blue text-white'
                            : 'border border-light-border dark:border-dark-border hover:bg-light-muted dark:hover:bg-dark-muted'
                        )}
                      >
                        {pageNumber + 1}
                      </button>
                    );
                  }
                )}

                <button
                  onClick={() => table.nextPage()}
                  disabled={!table.getCanNextPage()}
                  className="px-3 py-1 text-sm border border-light-border dark:border-dark-border rounded hover:bg-light-muted dark:hover:bg-dark-muted disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          )}
      </div>
    </div>
  );
}
