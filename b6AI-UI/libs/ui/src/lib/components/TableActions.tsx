import React from 'react';
import {
  RefreshCwIcon,
  DownloadIcon,
  PlusIcon,
  SettingsIcon,
  MoreHorizontalIcon,
} from 'lucide-react';
import { cn } from '../utils';

export interface TableAction {
  label: string;
  icon?: React.ReactNode;
  onClick: () => void;
  variant?: 'default' | 'primary' | 'secondary' | 'destructive';
  disabled?: boolean;
}

export interface TableActionsProps {
  // Common actions
  onRefresh?: () => void;
  onExport?: () => void;
  onCreate?: () => void;

  // Custom actions
  actions?: TableAction[];

  // Loading state
  isLoading?: boolean;

  // Column visibility toggle
  onToggleColumns?: () => void;
  showColumnToggle?: boolean;

  className?: string;
}

export const TableActions: React.FC<TableActionsProps> = ({
  onRefresh,
  onExport,
  onCreate,
  actions = [],
  isLoading = false,
  onToggleColumns,
  showColumnToggle = false,
  className,
}) => {
  const getActionButtonClass = (
    variant: TableAction['variant'] = 'default'
  ) => {
    const baseClass =
      'px-3 py-2 text-sm rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed';

    switch (variant) {
      case 'primary':
        return cn(baseClass, 'bg-b6ai-blue text-white hover:bg-b6ai-blue/90');
      case 'secondary':
        return cn(
          baseClass,
          'bg-light-muted dark:bg-dark-muted text-light-foreground dark:text-dark-foreground hover:bg-light-muted/80 dark:hover:bg-dark-muted/80'
        );
      case 'destructive':
        return cn(baseClass, 'bg-red-500 text-white hover:bg-red-600');
      default:
        return cn(
          baseClass,
          'border border-light-border dark:border-dark-border hover:bg-light-muted dark:hover:bg-dark-muted'
        );
    }
  };

  const iconButtonClass =
    'p-2 text-light-muted-foreground dark:text-dark-muted-foreground hover:text-light-foreground dark:hover:text-dark-foreground rounded hover:bg-light-muted dark:hover:bg-dark-muted disabled:opacity-50 disabled:cursor-not-allowed';

  return (
    <div className={cn('flex items-center gap-2', className)}>
      {/* Create Button */}
      {onCreate && (
        <button onClick={onCreate} className={getActionButtonClass('primary')}>
          <PlusIcon size={16} className="mr-2" />
          Create
        </button>
      )}

      {/* Custom Actions */}
      {actions.map((action, index) => (
        <button
          key={index}
          onClick={action.onClick}
          disabled={action.disabled}
          className={getActionButtonClass(action.variant)}
        >
          {action.icon && <span className="mr-2">{action.icon}</span>}
          {action.label}
        </button>
      ))}

      {/* Separator if we have both buttons and icon actions */}
      {(onCreate || actions.length > 0) &&
        (onRefresh || onExport || showColumnToggle) && (
          <div className="w-px h-6 bg-light-border dark:border-dark-border" />
        )}

      {/* Icon Actions */}
      <div className="flex items-center gap-1">
        {/* Column Visibility Toggle */}
        {showColumnToggle && onToggleColumns && (
          <button
            onClick={onToggleColumns}
            className={iconButtonClass}
            title="Toggle columns"
          >
            <SettingsIcon size={16} />
          </button>
        )}

        {/* Refresh */}
        {onRefresh && (
          <button
            onClick={onRefresh}
            disabled={isLoading}
            className={iconButtonClass}
            title="Refresh"
          >
            <RefreshCwIcon
              size={16}
              className={isLoading ? 'animate-spin' : ''}
            />
          </button>
        )}

        {/* Export */}
        {onExport && (
          <button onClick={onExport} className={iconButtonClass} title="Export">
            <DownloadIcon size={16} />
          </button>
        )}
      </div>
    </div>
  );
};

// Utility component for dropdown actions
export interface DropdownActionsProps {
  actions: TableAction[];
  trigger?: React.ReactNode;
  className?: string;
}

export const DropdownActions: React.FC<DropdownActionsProps> = ({
  actions,
  trigger,
  className,
}) => {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <div className={cn('relative', className)}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 text-light-muted-foreground dark:text-dark-muted-foreground hover:text-light-foreground dark:hover:text-dark-foreground rounded hover:bg-light-muted dark:hover:bg-dark-muted"
      >
        {trigger || <MoreHorizontalIcon size={16} />}
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />

          {/* Dropdown */}
          <div className="absolute right-0 top-full mt-1 z-20 min-w-[160px] bg-light-card dark:bg-dark-card border border-light-border dark:border-dark-border rounded-lg shadow-lg">
            {actions.map((action, index) => (
              <button
                key={index}
                onClick={() => {
                  action.onClick();
                  setIsOpen(false);
                }}
                disabled={action.disabled}
                className="w-full px-3 py-2 text-left text-sm hover:bg-light-muted dark:hover:bg-dark-muted disabled:opacity-50 disabled:cursor-not-allowed first:rounded-t-lg last:rounded-b-lg"
              >
                <div className="flex items-center">
                  {action.icon && <span className="mr-2">{action.icon}</span>}
                  {action.label}
                </div>
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

// Utility component for bulk actions
export interface BulkActionsProps {
  selectedCount: number;
  actions: TableAction[];
  onClearSelection: () => void;
  className?: string;
}

export const BulkActions: React.FC<BulkActionsProps> = ({
  selectedCount,
  actions,
  onClearSelection,
  className,
}) => {
  if (selectedCount === 0) return null;

  return (
    <div
      className={cn(
        'flex items-center gap-2 p-3 bg-light-muted dark:bg-dark-muted rounded-lg',
        className
      )}
    >
      <span className="text-sm font-medium">
        {selectedCount} item{selectedCount > 1 ? 's' : ''} selected
      </span>

      <div className="flex items-center gap-2 ml-auto">
        {actions.map((action, index) => (
          <button
            key={index}
            onClick={action.onClick}
            disabled={action.disabled}
            className="px-3 py-1 text-sm rounded border border-light-border dark:border-dark-border hover:bg-light-background dark:hover:bg-dark-background disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {action.icon && <span className="mr-1">{action.icon}</span>}
            {action.label}
          </button>
        ))}

        <button
          onClick={onClearSelection}
          className="px-3 py-1 text-sm rounded border border-light-border dark:border-dark-border hover:bg-light-background dark:hover:bg-dark-background"
        >
          Clear
        </button>
      </div>
    </div>
  );
};
