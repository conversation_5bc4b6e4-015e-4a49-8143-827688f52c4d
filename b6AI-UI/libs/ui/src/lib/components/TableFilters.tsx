import React from 'react';
import { CalendarIcon, FilterIcon, XIcon } from 'lucide-react';
import { cn } from '../utils';

export interface FilterOption {
  label: string;
  value: string;
}

export interface DateRangeFilter {
  start: string;
  end: string;
}

export interface TableFiltersProps {
  // Filter buttons
  filterOptions?: {
    label: string;
    value: string;
    active: boolean;
    onClick: () => void;
  }[];
  
  // Date range filter
  dateRange?: DateRangeFilter;
  onDateRangeChange?: (range: DateRangeFilter) => void;
  dateRangeLabel?: string;
  
  // Custom filter components
  children?: React.ReactNode;
  
  // Active filter indicators
  activeFilters?: {
    label: string;
    value: string;
    onRemove: () => void;
  }[];
  
  className?: string;
}

export const TableFilters: React.FC<TableFiltersProps> = ({
  filterOptions = [],
  dateRange,
  onDateRangeChange,
  dateRangeLabel = "Date Range",
  children,
  activeFilters = [],
  className,
}) => {
  return (
    <div className={cn("space-y-4", className)}>
      {/* Filter Buttons */}
      {filterOptions.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {filterOptions.map((option) => (
            <button
              key={option.value}
              className={cn(
                "px-3 py-1 text-xs rounded transition-colors",
                option.active
                  ? "bg-light-muted dark:bg-dark-muted text-light-foreground dark:text-dark-foreground"
                  : "border border-light-border dark:border-dark-border hover:bg-light-muted/50 dark:hover:bg-dark-muted/50"
              )}
              onClick={option.onClick}
            >
              {option.label}
            </button>
          ))}
        </div>
      )}

      {/* Date Range Filter */}
      {onDateRangeChange && (
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="relative">
            <input
              type="date"
              placeholder="Start Date"
              className="pl-10 pr-4 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
              value={dateRange?.start || ''}
              onChange={(e) =>
                onDateRangeChange({
                  start: e.target.value,
                  end: dateRange?.end || '',
                })
              }
            />
            <CalendarIcon
              size={16}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-light-muted-foreground dark:text-dark-muted-foreground"
            />
          </div>
          <div className="relative">
            <input
              type="date"
              placeholder="End Date"
              className="pl-10 pr-4 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
              value={dateRange?.end || ''}
              onChange={(e) =>
                onDateRangeChange({
                  start: dateRange?.start || '',
                  end: e.target.value,
                })
              }
            />
            <CalendarIcon
              size={16}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-light-muted-foreground dark:text-dark-muted-foreground"
            />
          </div>
        </div>
      )}

      {/* Custom Filter Components */}
      {children && <div className="flex flex-wrap gap-2">{children}</div>}

      {/* Active Filter Indicators */}
      {activeFilters.length > 0 && (
        <div className="flex flex-wrap items-center gap-2 text-sm text-light-muted-foreground dark:text-dark-muted-foreground">
          <span className="flex items-center">
            <FilterIcon size={14} className="mr-1" />
            Filtered by:
          </span>
          {activeFilters.map((filter, index) => (
            <span
              key={index}
              className="flex items-center px-2 py-1 rounded-full bg-light-muted dark:bg-dark-muted text-light-foreground dark:text-dark-foreground"
            >
              {filter.label}: {filter.value}
              <button
                onClick={filter.onRemove}
                className="ml-1 hover:text-red-500"
              >
                <XIcon size={12} />
              </button>
            </span>
          ))}
        </div>
      )}
    </div>
  );
};

// Utility component for creating filter buttons
export interface FilterButtonProps {
  label: string;
  active: boolean;
  onClick: () => void;
  className?: string;
}

export const FilterButton: React.FC<FilterButtonProps> = ({
  label,
  active,
  onClick,
  className,
}) => {
  return (
    <button
      className={cn(
        "px-3 py-1 text-xs rounded transition-colors",
        active
          ? "bg-light-muted dark:bg-dark-muted text-light-foreground dark:text-dark-foreground"
          : "border border-light-border dark:border-dark-border hover:bg-light-muted/50 dark:hover:bg-dark-muted/50",
        className
      )}
      onClick={onClick}
    >
      {label}
    </button>
  );
};

// Utility component for select-based filters
export interface FilterSelectProps {
  label: string;
  value: string;
  options: FilterOption[];
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export const FilterSelect: React.FC<FilterSelectProps> = ({
  label,
  value,
  options,
  onChange,
  placeholder = "Select...",
  className,
}) => {
  return (
    <div className={cn("flex flex-col gap-1", className)}>
      <label className="text-xs font-medium text-light-muted-foreground dark:text-dark-muted-foreground">
        {label}
      </label>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
      >
        <option value="">{placeholder}</option>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
};
