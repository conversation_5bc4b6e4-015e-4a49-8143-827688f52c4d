import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { cn } from '../utils';

// Common column utilities and components

// Status badge component
export interface StatusBadgeProps {
  status: string;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
  className?: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  variant = 'default',
  className,
}) => {
  const getVariantClass = () => {
    switch (variant) {
      case 'success':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'warning':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200';
      case 'error':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'info':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  return (
    <span
      className={cn(
        'px-2 py-1 rounded-full text-xs font-medium',
        getVariantClass(),
        className
      )}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

// Date formatter component
export interface DateCellProps {
  date: string | Date;
  format?: 'short' | 'long' | 'relative';
  className?: string;
}

export const DateCell: React.FC<DateCellProps> = ({
  date,
  format = 'short',
  className,
}) => {
  const formatDate = (dateValue: string | Date) => {
    const dateObj = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;
    
    switch (format) {
      case 'long':
        return dateObj.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
        });
      case 'relative':
        const now = new Date();
        const diffInMs = now.getTime() - dateObj.getTime();
        const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
        
        if (diffInDays === 0) return 'Today';
        if (diffInDays === 1) return 'Yesterday';
        if (diffInDays < 7) return `${diffInDays} days ago`;
        if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
        if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
        return `${Math.floor(diffInDays / 365)} years ago`;
      default:
        return dateObj.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        });
    }
  };

  return (
    <span className={className} title={new Date(date).toLocaleString()}>
      {formatDate(date)}
    </span>
  );
};

// Avatar component for user columns
export interface AvatarCellProps {
  name: string;
  email?: string;
  avatar?: string;
  className?: string;
}

export const AvatarCell: React.FC<AvatarCellProps> = ({
  name,
  email,
  avatar,
  className,
}) => {
  const initials = name
    .split(' ')
    .map((n) => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);

  return (
    <div className={cn('flex items-center', className)}>
      <div className="flex-shrink-0 h-8 w-8">
        {avatar ? (
          <img
            className="h-8 w-8 rounded-full"
            src={avatar}
            alt={name}
          />
        ) : (
          <div className="h-8 w-8 rounded-full bg-light-muted dark:bg-dark-muted flex items-center justify-center text-xs font-medium">
            {initials}
          </div>
        )}
      </div>
      <div className="ml-3">
        <div className="text-sm font-medium">{name}</div>
        {email && (
          <div className="text-xs text-light-muted-foreground dark:text-dark-muted-foreground">
            {email}
          </div>
        )}
      </div>
    </div>
  );
};

// Actions cell component
export interface ActionsCellProps {
  actions: {
    label: string;
    icon?: React.ReactNode;
    onClick: () => void;
    variant?: 'default' | 'destructive';
  }[];
  className?: string;
}

export const ActionsCell: React.FC<ActionsCellProps> = ({
  actions,
  className,
}) => {
  return (
    <div className={cn('flex items-center gap-1', className)}>
      {actions.map((action, index) => (
        <button
          key={index}
          onClick={action.onClick}
          className={cn(
            'p-1 rounded hover:bg-light-muted dark:hover:bg-dark-muted',
            action.variant === 'destructive'
              ? 'text-red-500 hover:text-red-700'
              : 'text-light-muted-foreground dark:text-dark-muted-foreground hover:text-light-foreground dark:hover:text-dark-foreground'
          )}
          title={action.label}
        >
          {action.icon}
        </button>
      ))}
    </div>
  );
};

// Utility functions for creating common column definitions

// Create a status column
export const createStatusColumn = <T,>(
  accessorKey: keyof T,
  header: string = 'Status',
  getVariant?: (status: string) => StatusBadgeProps['variant']
): ColumnDef<T> => ({
  accessorKey: accessorKey as string,
  header,
  cell: ({ getValue }) => {
    const status = getValue() as string;
    const variant = getVariant ? getVariant(status) : 'default';
    return <StatusBadge status={status} variant={variant} />;
  },
});

// Create a date column
export const createDateColumn = <T,>(
  accessorKey: keyof T,
  header: string = 'Date',
  format: DateCellProps['format'] = 'short'
): ColumnDef<T> => ({
  accessorKey: accessorKey as string,
  header,
  cell: ({ getValue }) => {
    const date = getValue() as string | Date;
    return <DateCell date={date} format={format} />;
  },
});

// Create an avatar column
export const createAvatarColumn = <T,>(
  nameAccessor: keyof T,
  emailAccessor?: keyof T,
  avatarAccessor?: keyof T,
  header: string = 'User'
): ColumnDef<T> => ({
  accessorKey: nameAccessor as string,
  header,
  cell: ({ row }) => {
    const name = row.getValue(nameAccessor as string) as string;
    const email = emailAccessor ? (row.getValue(emailAccessor as string) as string) : undefined;
    const avatar = avatarAccessor ? (row.getValue(avatarAccessor as string) as string) : undefined;
    
    return <AvatarCell name={name} email={email} avatar={avatar} />;
  },
});

// Create an actions column
export const createActionsColumn = <T,>(
  getActions: (row: T) => ActionsCellProps['actions']
): ColumnDef<T> => ({
  id: 'actions',
  header: 'Actions',
  cell: ({ row }) => {
    const actions = getActions(row.original);
    return <ActionsCell actions={actions} />;
  },
  enableSorting: false,
  enableHiding: false,
});

// Create a simple text column with optional formatting
export const createTextColumn = <T,>(
  accessorKey: keyof T,
  header: string,
  formatter?: (value: any) => string
): ColumnDef<T> => ({
  accessorKey: accessorKey as string,
  header,
  cell: ({ getValue }) => {
    const value = getValue();
    return formatter ? formatter(value) : String(value);
  },
});

// Create a numeric column with optional formatting
export const createNumericColumn = <T,>(
  accessorKey: keyof T,
  header: string,
  formatter?: (value: number) => string
): ColumnDef<T> => ({
  accessorKey: accessorKey as string,
  header,
  cell: ({ getValue }) => {
    const value = getValue() as number;
    return formatter ? formatter(value) : value.toLocaleString();
  },
});
