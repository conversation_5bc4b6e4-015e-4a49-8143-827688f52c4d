{"name": "@b6ai/ui", "version": "0.0.1", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"development": "./src/index.ts", "types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}, "./utils": {"development": "./src/utils.ts", "types": "./dist/utils.d.ts", "import": "./dist/utils.js", "default": "./dist/utils.js"}, "./server": {"development": "./src/server.ts", "types": "./dist/server.d.ts", "import": "./dist/server.js", "default": "./dist/server.js"}, "./styles/globals.css": "./dist/ui.css"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-table": "^8.20.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.543.0", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}}