{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/*.spec.ts", "!{projectRoot}/**/*.test.ts"], "sharedGlobals": []}, "targetDefaults": {"build": {"inputs": ["production"], "outputs": ["{projectRoot}/dist"]}, "lint": {"inputs": ["default"]}, "test": {"inputs": ["default", "^production"]}}, "workspaceLayout": {"appsDir": "apps", "libsDir": "packages"}, "nxCloudId": "68b53d7205225a1a8db82025"}