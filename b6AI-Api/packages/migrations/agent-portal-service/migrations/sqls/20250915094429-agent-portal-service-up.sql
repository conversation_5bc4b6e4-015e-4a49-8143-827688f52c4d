CREATE SCHEMA IF NOT EXISTS main;

SET search_path TO main,public;
GRANT ALL ON SCHEMA main TO public;

-- 🔹 Departments Table
CREATE TABLE IF NOT EXISTS main.departments (
    id          uuid DEFAULT gen_random_uuid() NOT NULL,   -- primary key
    name        varchar(100) NOT NULL,                     -- department name
    status      varchar(20) DEFAULT 'active' NOT NULL,     -- active/inactive
    created_on  timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted     bool DEFAULT false NOT NULL,
    deleted_on  timestamptz,
    deleted_by  uuid,
    CONSTRAINT pk_departments_id PRIMARY KEY (id)
);

-- 🔹 Agents Table (with foreign key to departments)
CREATE TABLE IF NOT EXISTS main.agents (
    id              uuid DEFAULT gen_random_uuid() NOT NULL,   -- primary key
    department_id   uuid REFERENCES main.departments(id) ON DELETE SET NULL, -- FK
    keycloak_id     varchar(255) NOT NULL,                     -- Keycloak user ID
    profile_image   varchar(500),                              -- URL/path to profile image
    created_on      timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on     timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted         bool DEFAULT false NOT NULL,
    deleted_on      timestamptz,
    deleted_by      uuid,
    CONSTRAINT pk_agents_id PRIMARY KEY (id)
);

INSERT INTO main.departments ( name, status)
VALUES ('Default', 'active');

-- 🔹 Indexes
CREATE INDEX IF NOT EXISTS idx_agents_department_id ON main.agents (department_id);
CREATE INDEX IF NOT EXISTS idx_agents_keycloak_id ON main.agents (keycloak_id);
CREATE INDEX IF NOT EXISTS idx_departments_status ON main.departments (status);

-- 🔹 Trigger function to auto-update modified_on
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.modified_on = now();
   RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';

-- 🔹 Triggers
CREATE TRIGGER trg_departments_set_modified
BEFORE UPDATE ON main.departments
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER trg_agents_set_modified
BEFORE UPDATE ON main.agents
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
