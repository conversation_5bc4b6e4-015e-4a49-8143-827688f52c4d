CREATE SCHEMA IF NOT EXISTS main;

SET search_path TO main,public;
GRANT ALL ON SCHEMA main TO public;

CREATE TABLE IF NOT EXISTS main.features (
    id           uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    feature_code varchar(50) NOT NULL, -- e.g., "MAX_BOTS"
    name         varchar(100) NOT NULL, -- human-readable name
    description  text,                  -- optional details
    created_on   timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on  timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                bool DEFAULT false NOT NULL,
    deleted_on             timestamptz,
    deleted_by             uuid,
    CONSTRAINT pk_features PRIMARY KEY (id),
    CONSTRAINT uq_feature_code UNIQUE(feature_code)
);

CREATE TABLE IF NOT EXISTS main.plans (
    id                     uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    plan_code              varchar(50) NOT NULL, -- internal code (e.g. "BOT_BUILDER_BASIC")
    name                   varchar(100) NOT NULL,
    description            text,
    price_cents            integer NOT NULL, -- price in cents for accuracy
    year_price_cents       integer NOT NULL, -- year price in cents for accuracy
    currency               varchar(10) DEFAULT 'USD' NOT NULL,
    billing_interval       varchar(20) DEFAULT 'monthly' NOT NULL, -- monthly, yearly, etc.
    gateway_name           varchar(50) NOT NULL, -- e.g., 'stripe', 'chargebee'
    gateway_ref_id         varchar(100) NOT NULL, -- plan id in gateway
    created_on             timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on            timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                bool DEFAULT false NOT NULL,
    deleted_on             timestamptz,
    deleted_by             uuid,
    CONSTRAINT pk_plans_id PRIMARY KEY (id),
    CONSTRAINT uq_plans_plan_code UNIQUE (plan_code)
);

CREATE TABLE IF NOT EXISTS main.plan_features (
    id         uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    plan_id    uuid NOT NULL REFERENCES main.plans(id) ON DELETE CASCADE,
    feature_id uuid NOT NULL REFERENCES main.features(id) ON DELETE CASCADE,
    value      varchar(100) NOT NULL, -- e.g., "3", "Unlimited", "Yes"
    created_on timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                bool DEFAULT false NOT NULL,
    deleted_on             timestamptz,
    deleted_by             uuid,
    CONSTRAINT pk_plan_features PRIMARY KEY (id),
    CONSTRAINT uq_plan_feature UNIQUE(plan_id, feature_id)
);

CREATE TABLE IF NOT EXISTS main.subscriptions (
    id                     uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    tenant_id              uuid NOT NULL, -- subscriber (tenant)
    plan_id                uuid NOT NULL REFERENCES main.plans(id),
    status                 varchar(20) DEFAULT 'active' NOT NULL, -- active, trialing, canceled, past_due
    start_date             timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    end_date               timestamptz,
    trial_end_date         timestamptz,
    renewal_date           timestamptz,
    billing_period         integer DEFAULT 1 NOT NULL, -- e.g. 1, 3, 12
    billing_period_unit    varchar(20) DEFAULT 'month' NOT NULL, -- day, week, month, year
    gateway_name           varchar(50) NOT NULL, -- stripe, chargebee, etc.
    gateway_ref_id         varchar(100) NOT NULL, -- subscription id from gateway
    created_on             timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on            timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                bool DEFAULT false NOT NULL,
    deleted_on             timestamptz,
    deleted_by             uuid,
    CONSTRAINT pk_subscriptions_id PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS main.billing_customers (
    id              uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    tenant_id       uuid NOT NULL, 
    first_name      varchar(100),
    last_name       varchar(100),
    email           varchar(150) NOT NULL,
    company         varchar(150),
    gateway_name    varchar(50) NOT NULL, 
    gateway_ref_id  varchar(100) NOT NULL, 
    created_on      timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on     timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted         bool DEFAULT false NOT NULL,
    deleted_on      timestamptz,
    deleted_by      uuid,
    CONSTRAINT pk_billing_customers_id PRIMARY KEY (id)
);


INSERT INTO main.features (feature_code, name, description) VALUES
('multi_channel_support', 'Multi-channel Support', 'Connect your bot to web chat, WhatsApp, Messenger, Slack, and other channels.'),
('faq_bot', 'FAQ Bot', 'Create FAQ bots to answer customer queries automatically.'),
('custom_flows', 'Custom Conversation Flows', 'Design custom conversation flows using drag-and-drop studio.'),
('nlp_intents', 'Intent Recognition', 'Define and manage intents for natural language understanding.'),
('multi_language', 'Multi-language Support', 'Support multiple languages for your bots.'),
('analytics_dashboard', 'Analytics & Reporting', 'Monitor bot performance, conversations, and user engagement.'),
('team_collaboration', 'Team Collaboration', 'Multiple team members can manage bots with role-based access.'),
('handover_to_agent', 'Handover to Agent', 'Seamlessly transfer conversations to human agents.'),
('integration_api', 'API & Integrations', 'Connect your bot to external APIs and services.'),
('user_segmentation', 'User Segmentation', 'Target messages and workflows based on user segments.'),
('webhook_events', 'Webhook & Events', 'Trigger webhooks for bot events and responses.'),
('custom_branding', 'Custom Branding', 'Add your own branding, colors, and logos to the bot interface.'),
('priority_support', 'Priority Support', 'Get faster support response from our team.'),
('custom_ai_integration', 'Custom AI Integrations', 'Connect your own AI model API keys for bot responses.'),
('scheduled_messages', 'Scheduled Messages', 'Send messages to users at scheduled times.'),
('rich_messages', 'Rich Media Messages', 'Send images, videos, buttons, and carousels in conversations.'),
('versioning', 'Bot Versioning', 'Maintain multiple versions of your bots and rollback if needed.'),
('enterprise_security', 'Enterprise Security', 'Advanced security features like SSO, IP whitelisting, and audit logs.'),
('sla_uptime', 'SLA & Uptime', 'Guaranteed uptime and support response time for Enterprise customers.'),
('custom_enterprise_features', 'Custom Enterprise Features', 'Additional custom features for enterprise clients.');




-- Indexes
CREATE INDEX idx_plans_gateway_name ON main.plans(gateway_name);
CREATE INDEX idx_plans_gateway_ref_id ON main.plans(gateway_ref_id);
CREATE INDEX idx_plans_billing_interval ON main.plans(billing_interval);
CREATE INDEX idx_plans_deleted ON main.plans(deleted);

CREATE INDEX idx_subscriptions_tenant_id ON main.subscriptions(tenant_id);
CREATE INDEX idx_subscriptions_plan_id ON main.subscriptions(plan_id);
CREATE INDEX idx_subscriptions_status ON main.subscriptions(status);
CREATE INDEX idx_subscriptions_gateway_name ON main.subscriptions(gateway_name);
CREATE INDEX idx_subscriptions_gateway_ref_id ON main.subscriptions(gateway_ref_id);
CREATE INDEX idx_subscriptions_deleted ON main.subscriptions(deleted);

-- Features Table Indexes
CREATE INDEX idx_features_feature_code ON main.features(feature_code);
CREATE INDEX idx_features_name ON main.features(name);
CREATE INDEX idx_features_deleted ON main.features(deleted);

-- Plan Features Table Indexes
CREATE INDEX idx_plan_features_plan_id ON main.plan_features(plan_id);
CREATE INDEX idx_plan_features_feature_id ON main.plan_features(feature_id);
CREATE INDEX idx_plan_features_value ON main.plan_features(value);
CREATE INDEX idx_plan_features_created_on ON main.plan_features(created_on);
CREATE INDEX idx_plan_features_modified_on ON main.plan_features(modified_on);
CREATE INDEX idx_plan_features_deleted ON main.plan_features(deleted);

-- Billing Customer indexes
CREATE INDEX idx_billing_customers_tenant_id ON main.billing_customers(tenant_id);
CREATE INDEX idx_billing_customers_email ON main.billing_customers(email);
CREATE INDEX idx_billing_customers_company ON main.billing_customers(company);
CREATE INDEX idx_billing_customers_created_on ON main.billing_customers(created_on);
CREATE INDEX idx_billing_customers_modified_on ON main.billing_customers(modified_on);
CREATE INDEX idx_billing_customers_deleted ON main.billing_customers(deleted);


-- Function
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.modified_on = now();
   RETURN NEW;
END;
$$ language 'plpgsql';

-- Attach trigger to plans
CREATE TRIGGER trg_plans_set_modified
BEFORE UPDATE ON main.plans
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Attach trigger to subscriptions
CREATE TRIGGER trg_subscriptions_set_modified
BEFORE UPDATE ON main.subscriptions
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Attach trigger to features
CREATE TRIGGER trg_plans_set_modified
BEFORE UPDATE ON main.features
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Attach trigger to plan_features
CREATE TRIGGER trg_plans_set_modified
BEFORE UPDATE ON main.plan_features
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();


-- Trigger to auto-update modified_on timestamp
CREATE TRIGGER trg_billing_customers_set_modified
BEFORE UPDATE ON main.billing_customers
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();