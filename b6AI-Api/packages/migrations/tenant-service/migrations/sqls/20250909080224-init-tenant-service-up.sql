CREATE SCHEMA IF NOT EXISTS main;

SET search_path TO main,public;
GRANT ALL ON SCHEMA main TO public;


CREATE TABLE IF NOT EXISTS main.tenants (
    id              uuid DEFAULT gen_random_uuid() NOT NULL,
    name            varchar(100) NOT NULL,          -- tenant/company name
    domain          varchar(255) NOT NULL,          -- e.g. myapp.example.com
    status          varchar(20) DEFAULT 'pending' NOT NULL, -- pending , provisioning, active, inactive, suspended, deleted
    owner_id        uuid,                           -- user id of tenant owner/admin
    contact_email   varchar(150),                   -- support/owner contact
    contact_phone   varchar(30),                    -- optional phone
    address         text,                           -- optional business address
    settings        jsonb DEFAULT '{}'::jsonb,      -- customizable settings per tenant
    created_on      timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on     timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted         bool DEFAULT false NOT NULL,
    deleted_on      timestamptz,
    deleted_by      uuid,
    CONSTRAINT pk_tenants_id PRIMARY KEY (id),
    CONSTRAINT uq_tenants_domain UNIQUE (domain)
);

-- 🔹 Useful Indexes
CREATE INDEX IF NOT EXISTS idx_tenants_status ON main.tenants (status);
CREATE INDEX IF NOT EXISTS idx_tenants_domain ON main.tenants (domain);
-- CREATE INDEX IF NOT EXISTS idx_tenants_plan_id ON main.tenants (plan_id); -- plan_id does not exist
CREATE INDEX IF NOT EXISTS idx_tenants_owner_id ON main.tenants (owner_id);

-- Optional: Partial index for only active tenants
CREATE INDEX IF NOT EXISTS idx_tenants_active_domain ON main.tenants (domain) WHERE status = 'active';


-- Trigger function (already exists, but redefine if needed)
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.modified_on = now();
   RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';



-- 🔹 Attach trigger to tenants
CREATE TRIGGER trg_tenants_set_modified
BEFORE UPDATE ON main.tenants
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
