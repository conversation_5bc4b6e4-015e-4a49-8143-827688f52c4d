-- Drop triggers first
DROP TRIGGER IF EXISTS trg_business_hours_departments_set_modified ON main.business_hours_departments;
DROP TRIGGER IF EXISTS trg_out_of_office_settings_set_modified ON main.out_of_office_settings;
DROP TRIGGER IF EXISTS trg_holidays_set_modified ON main.holidays;
DROP TRIGGER IF EXISTS trg_lunch_breaks_set_modified ON main.lunch_breaks;
DROP TRIGGER IF EXISTS trg_working_hours_set_modified ON main.working_hours;
DROP TRIGGER IF EXISTS trg_working_days_set_modified ON main.working_days;
DROP TRIGGER IF EXISTS trg_business_hours_set_modified ON main.business_hours;

-- Drop indexes
DROP INDEX IF EXISTS main.idx_business_hours_tenant_default;
DROP INDEX IF EXISTS main.idx_out_of_office_settings_tenant_id;
DROP INDEX IF EXISTS main.idx_holidays_type;
DROP INDEX IF EXISTS main.idx_holidays_date;
DROP INDEX IF EXISTS main.idx_holidays_tenant_id;
DROP INDEX IF EXISTS main.idx_lunch_breaks_business_hours_id;
DROP INDEX IF EXISTS main.idx_working_hours_working_day_id;
DROP INDEX IF EXISTS main.idx_working_days_day_of_week;
DROP INDEX IF EXISTS main.idx_working_days_business_hours_id;
DROP INDEX IF EXISTS main.idx_business_hours_departments_department_id;
DROP INDEX IF EXISTS main.idx_business_hours_departments_business_hours_id;
DROP INDEX IF EXISTS main.idx_business_hours_is_default;
DROP INDEX IF EXISTS main.idx_business_hours_status;
DROP INDEX IF EXISTS main.idx_business_hours_tenant_id;

-- Drop tables in correct order (respecting foreign key constraints)
-- Note: departments table is managed in agent-portal-service
DROP TABLE IF EXISTS main.business_hours_departments;
DROP TABLE IF EXISTS main.working_hours;
DROP TABLE IF EXISTS main.working_days;
DROP TABLE IF EXISTS main.lunch_breaks;
DROP TABLE IF EXISTS main.out_of_office_settings;
DROP TABLE IF EXISTS main.holidays;
DROP TABLE IF EXISTS main.business_hours;