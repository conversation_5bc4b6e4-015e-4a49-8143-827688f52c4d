CREATE SCHEMA IF NOT EXISTS main;

SET search_path TO main,public;
GRANT ALL ON SCHEMA main TO public;

-- 🔹 Business Hours Configuration Tables
-- Note: Departments are managed in agent-portal-service

-- Business Hours Table (main configuration)
CREATE TABLE IF NOT EXISTS main.business_hours (
    id              uuid DEFAULT gen_random_uuid() NOT NULL,
    tenant_id       uuid NOT NULL,                             -- tenant reference
    name            varchar(100) NOT NULL,                     -- configuration name
    timezone        varchar(50) NOT NULL,                      -- timezone (e.g., 'America/New_York')
    is_default      bool DEFAULT false NOT NULL,              -- flag to mark as default configuration
    status          varchar(20) DEFAULT 'active' NOT NULL,     -- active/inactive
    created_on      timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on     timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted         bool DEFAULT false NOT NULL,
    deleted_on      timestamptz,
    deleted_by      uuid,
    CONSTRAINT pk_business_hours_id PRIMARY KEY (id)
);

-- Working Days Table (Which days are working days)
CREATE TABLE IF NOT EXISTS main.working_days (
    id                  uuid DEFAULT gen_random_uuid() NOT NULL,
    business_hours_id   uuid NOT NULL REFERENCES main.business_hours(id) ON DELETE CASCADE,
    day_of_week         integer NOT NULL CHECK (day_of_week >= 1 AND day_of_week <= 7), -- 1=Monday, 7=Sunday
    is_working_day      bool DEFAULT true NOT NULL,                -- flag to indicate if this day is a working day
    created_on          timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on         timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted             bool DEFAULT false NOT NULL,
    deleted_on          timestamptz,
    deleted_by          uuid,
    CONSTRAINT pk_working_days_id PRIMARY KEY (id),
    CONSTRAINT uq_working_days_business_hours_day UNIQUE (business_hours_id, day_of_week)
);

-- Working Hours Table (specific hours for each working day)
CREATE TABLE IF NOT EXISTS main.working_hours (
    id                      uuid DEFAULT gen_random_uuid() NOT NULL,
    working_day_id          uuid NOT NULL REFERENCES main.working_days(id) ON DELETE CASCADE,
    start_time              time NOT NULL,                          -- start time (e.g., '09:00')
    end_time                time NOT NULL,                          -- end time (e.g., '17:00')
    created_on              timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on             timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                 bool DEFAULT false NOT NULL,
    deleted_on              timestamptz,
    deleted_by              uuid,
    CONSTRAINT pk_working_hours_id PRIMARY KEY (id),
    CONSTRAINT uq_working_hours_working_day UNIQUE (working_day_id),
    CONSTRAINT chk_working_hours_time CHECK (start_time < end_time)
);

-- Lunch Breaks Table (one per business hours configuration)
CREATE TABLE IF NOT EXISTS main.lunch_breaks (
    id                  uuid DEFAULT gen_random_uuid() NOT NULL,
    business_hours_id   uuid NOT NULL REFERENCES main.business_hours(id) ON DELETE CASCADE,
    start_time          time NOT NULL,                              -- lunch start time (e.g., '12:00')
    end_time            time NOT NULL,                              -- lunch end time (e.g., '13:00')
    created_on          timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on         timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted             bool DEFAULT false NOT NULL,
    deleted_on          timestamptz,
    deleted_by          uuid,
    CONSTRAINT pk_lunch_breaks_id PRIMARY KEY (id),
    CONSTRAINT uq_lunch_breaks_business_hours UNIQUE (business_hours_id),
    CONSTRAINT chk_lunch_breaks_time CHECK (start_time < end_time)
);

-- Business Hours Departments Junction Table (Many-to-Many)
-- Note: department_id references departments in agent-portal-service (no FK constraint)
CREATE TABLE IF NOT EXISTS main.business_hours_departments (
    id                  uuid DEFAULT gen_random_uuid() NOT NULL,
    business_hours_id   uuid NOT NULL REFERENCES main.business_hours(id) ON DELETE CASCADE,
    department_id       uuid NOT NULL,  -- references departments.id in agent-portal-service
    created_on          timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on         timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted             bool DEFAULT false NOT NULL,
    deleted_on          timestamptz,
    deleted_by          uuid,
    CONSTRAINT pk_business_hours_departments_id PRIMARY KEY (id),
    CONSTRAINT uq_business_hours_departments UNIQUE (business_hours_id, department_id)
);

-- Holidays Table
CREATE TABLE IF NOT EXISTS main.holidays (
    id              uuid DEFAULT gen_random_uuid() NOT NULL,
    tenant_id       uuid NOT NULL,                             -- tenant reference
    name            varchar(200) NOT NULL,                     -- holiday name
    date            date NOT NULL,                             -- holiday date
    type            varchar(20) DEFAULT 'global' NOT NULL,     -- 'global' or 'country_specific'
    country_code    varchar(3),                                -- ISO country code for country-specific holidays
    status          varchar(20) DEFAULT 'active' NOT NULL,     -- active/inactive
    created_on      timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on     timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted         bool DEFAULT false NOT NULL,
    deleted_on      timestamptz,
    deleted_by      uuid,
    CONSTRAINT pk_holidays_id PRIMARY KEY (id)
);

-- Out of Office Settings Table
CREATE TABLE IF NOT EXISTS main.out_of_office_settings (
    id                          uuid DEFAULT gen_random_uuid() NOT NULL,
    tenant_id                   uuid NOT NULL,                             -- tenant reference
    after_hours_message         text,                                      -- message for after business hours
    enable_after_hours_message  bool DEFAULT false NOT NULL,              -- flag to enable after hours message
    holiday_message             text,                                      -- message for holidays
    enable_holiday_message      bool DEFAULT false NOT NULL,              -- flag to enable holiday message
    collect_contact_info        bool DEFAULT false NOT NULL,              -- flag to collect contact info for follow-up
    emergency_contact_info      varchar(255),                              -- emergency contact (email or phone)
    enable_emergency_contact    bool DEFAULT false NOT NULL,              -- flag to enable emergency contact
    status                      varchar(20) DEFAULT 'active' NOT NULL,     -- active/inactive
    created_on                  timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on                 timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                     bool DEFAULT false NOT NULL,
    deleted_on                  timestamptz,
    deleted_by                  uuid,
    CONSTRAINT pk_out_of_office_settings_id PRIMARY KEY (id),
    CONSTRAINT uq_out_of_office_settings_tenant UNIQUE (tenant_id)
);

-- 🔹 Additional Indexes for Business Hours System
CREATE INDEX IF NOT EXISTS idx_business_hours_tenant_id ON main.business_hours (tenant_id);
CREATE INDEX IF NOT EXISTS idx_business_hours_status ON main.business_hours (status);
CREATE INDEX IF NOT EXISTS idx_business_hours_is_default ON main.business_hours (is_default);
CREATE INDEX IF NOT EXISTS idx_business_hours_departments_business_hours_id ON main.business_hours_departments (business_hours_id);
CREATE INDEX IF NOT EXISTS idx_business_hours_departments_department_id ON main.business_hours_departments (department_id);
CREATE INDEX IF NOT EXISTS idx_working_days_business_hours_id ON main.working_days (business_hours_id);
CREATE INDEX IF NOT EXISTS idx_working_days_day_of_week ON main.working_days (day_of_week);
CREATE INDEX IF NOT EXISTS idx_working_hours_working_day_id ON main.working_hours (working_day_id);
CREATE INDEX IF NOT EXISTS idx_lunch_breaks_business_hours_id ON main.lunch_breaks (business_hours_id);
CREATE INDEX IF NOT EXISTS idx_holidays_tenant_id ON main.holidays (tenant_id);
CREATE INDEX IF NOT EXISTS idx_holidays_date ON main.holidays (date);
CREATE INDEX IF NOT EXISTS idx_holidays_type ON main.holidays (type);
CREATE INDEX IF NOT EXISTS idx_out_of_office_settings_tenant_id ON main.out_of_office_settings (tenant_id);

-- 🔹 Trigger function to auto-update modified_on (may already exist)
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.modified_on = now();
   RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';

-- 🔹 Triggers
CREATE TRIGGER trg_business_hours_set_modified
BEFORE UPDATE ON main.business_hours
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER trg_working_days_set_modified
BEFORE UPDATE ON main.working_days
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER trg_working_hours_set_modified
BEFORE UPDATE ON main.working_hours
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER trg_lunch_breaks_set_modified
BEFORE UPDATE ON main.lunch_breaks
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER trg_holidays_set_modified
BEFORE UPDATE ON main.holidays
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER trg_out_of_office_settings_set_modified
BEFORE UPDATE ON main.out_of_office_settings
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER trg_business_hours_departments_set_modified
BEFORE UPDATE ON main.business_hours_departments
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- 🔹 Constraint to ensure only one default business hours per tenant
CREATE UNIQUE INDEX IF NOT EXISTS idx_business_hours_tenant_default
ON main.business_hours (tenant_id)
WHERE is_default = true AND deleted = false;

-- 🔹 Initial Data Inserts

-- Sample tenant ID for initial data
DO $$
DECLARE
    sample_tenant_id uuid := 'a1b2c3d4-e5f6-7890-abcd-ef1234567890';
    sample_department_id uuid := 'd1e2f3a4-b5c6-7890-1234-567890abcdef';
    business_hours_id uuid := '002f12a4-db95-3d9e-44a8-73fb8da3a485';
    working_day_mon uuid := '9e09af35-ccbe-57a6-860d-a79d7e254f74';
    working_day_tue uuid := '42ebee3b-be94-4774-e0a0-3f150716dc15';
    working_day_wed uuid := '271ebe3e-b443-57c2-e072-b2a659a7d963';
    working_day_thu uuid := '7639ea29-6552-df73-2da0-687ff128bd2d';
    working_day_fri uuid := '5c8439e5-0a9b-128b-5e5a-34a71370ab0d';
BEGIN
    -- Insert default business hours configuration
    INSERT INTO main.business_hours (id, tenant_id, name, timezone, is_default, status)
    VALUES (business_hours_id, sample_tenant_id, 'Default Business Hours', 'America/New_York', true, 'active')
    ON CONFLICT DO NOTHING;

    -- Insert working days (Monday to Friday)
    INSERT INTO main.working_days (id, business_hours_id, day_of_week, is_working_day) VALUES
    (working_day_mon, business_hours_id, 1, true), -- Monday
    (working_day_tue, business_hours_id, 2, true), -- Tuesday
    (working_day_wed, business_hours_id, 3, true), -- Wednesday
    (working_day_thu, business_hours_id, 4, true), -- Thursday
    (working_day_fri, business_hours_id, 5, true)  -- Friday
    ON CONFLICT DO NOTHING;

    -- Insert working hours (9 AM to 5 PM for all working days)
    INSERT INTO main.working_hours (working_day_id, start_time, end_time) VALUES
    (working_day_mon, '09:00', '17:00'), -- Monday 9-5
    (working_day_tue, '09:00', '17:00'), -- Tuesday 9-5
    (working_day_wed, '09:00', '17:00'), -- Wednesday 9-5
    (working_day_thu, '09:00', '17:00'), -- Thursday 9-5
    (working_day_fri, '09:00', '17:00')  -- Friday 9-5
    ON CONFLICT DO NOTHING;

    -- Insert lunch break (12 PM to 1 PM)
    INSERT INTO main.lunch_breaks (business_hours_id, start_time, end_time)
    VALUES (business_hours_id, '12:00', '13:00')
    ON CONFLICT DO NOTHING;

    -- Insert business hours department association
    INSERT INTO main.business_hours_departments (business_hours_id, department_id)
    VALUES (business_hours_id, sample_department_id)
    ON CONFLICT DO NOTHING;

    -- Insert sample holidays
    INSERT INTO main.holidays (tenant_id, name, date, type, status) VALUES
    (sample_tenant_id, 'New Year''s Day', '2024-01-01', 'global', 'active'),
    (sample_tenant_id, 'Independence Day', '2024-07-04', 'country_specific', 'active'),
    (sample_tenant_id, 'Labor Day', '2024-09-02', 'country_specific', 'active'),
    (sample_tenant_id, 'Thanksgiving', '2024-11-28', 'country_specific', 'active'),
    (sample_tenant_id, 'Christmas Day', '2024-12-25', 'global', 'active')
    ON CONFLICT DO NOTHING;

    -- Insert out of office settings
    INSERT INTO main.out_of_office_settings (
        tenant_id,
        after_hours_message,
        enable_after_hours_message,
        holiday_message,
        enable_holiday_message,
        collect_contact_info,
        emergency_contact_info,
        enable_emergency_contact,
        status
    ) VALUES (
        sample_tenant_id,
        'Thank you for contacting us. Our office hours are Monday to Friday, 9 AM to 5 PM EST. We will respond to your message during our next business day.',
        true,
        'Our office is currently closed for the holiday. We will respond to your message when we return. For urgent matters, please contact our emergency line.',
        true,
        true,
        '<EMAIL>',
        true,
        'active'
    ) ON CONFLICT DO NOTHING;

END $$;