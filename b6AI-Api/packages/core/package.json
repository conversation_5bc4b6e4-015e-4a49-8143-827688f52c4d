{"name": "@b6ai/core", "version": "0.0.1", "description": "Local core library for reused codes", "keywords": ["loopback-extension", "loopback"], "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": "20 || 22 || 24"}, "scripts": {"build": "lb-tsc", "build:watch": "lb-tsc --watch", "lint": "npm run eslint && npm run prettier:check", "lint:fix": "npm run eslint:fix && npm run prettier:fix", "prettier:cli": "lb-prettier \"**/*.ts\" \"**/*.js\"", "prettier:check": "npm run prettier:cli -- -l", "prettier:fix": "npm run prettier:cli -- --write", "eslint": "lb-eslint --report-unused-disable-directives .", "eslint:fix": "npm run eslint -- --fix", "pretest": "npm run rebuild", "test": "lb-mocha --allow-console-logs \"dist/__tests__\"", "posttest": "npm run lint", "test:dev": "lb-mocha --allow-console-logs dist/__tests__/**/*.js && npm run posttest", "clean": "lb-clean dist *.tsbuildinfo .eslintcache", "rebuild": "npm run clean && npm run build"}, "repository": {"type": "git", "url": ""}, "author": "", "license": "", "files": ["README.md", "dist", "src", "!*/__tests__"], "dependencies": {"@loopback/authentication": "^12.0.6", "@loopback/core": "^7.0.0", "@loopback/repository": "^8.0.0", "@loopback/rest": "^15.0.5", "@loopback/security": "^0.12.5", "@loopback/sequelize": "^0.8.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "pino": "^9.9.5", "pino-pretty": "^13.1.1"}, "devDependencies": {"@loopback/build": "^12.0.1", "@loopback/eslint-config": "^16.0.0", "@loopback/testlab": "^8.0.1", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^16.18.126", "eslint": "^8.57.1", "source-map-support": "^0.5.21", "tslib": "^2.0.0", "typescript": "~5.2.2"}}