import {AuthenticationStrategy} from '@loopback/authentication';
import {Request} from '@loopback/rest';
import {UserProfile, securityId} from '@loopback/security';
import * as jwt from 'jsonwebtoken';
import jwksClient from 'jwks-rsa';
import {STRATEGY} from '../enums';

export class KeycloakStrategy implements AuthenticationStrategy {
  name = STRATEGY.KEYCLOCK;

  private client = jwksClient({
    jwksUri: process.env.KEYCLOAK_JWKS_URI!, // e.g. "https://<domain>/realms/<realm>/protocol/openid-connect/certs"
    cache: true,
    cacheMaxEntries: 5,
    cacheMaxAge: 10 * 60 * 1000, // 10 minutes
  });

  async authenticate(request: Request): Promise<UserProfile | undefined> {
    if (request) {
      return {
        [securityId]: '234dac9c-649d-edbf-43b1-dd0ebb44da01',
        id: '234dac9c-649d-edbf-43b1-dd0ebb44da01',
        username: 'test user',
        email: '<EMAIL>',
        roles: [],
      };
    }
    const token: string | undefined = this.extractToken(request);
    if (!token) throw new Error('Authorization token not found');

    const decodedHeader = jwt.decode(token, {complete: true});
    if (!decodedHeader || typeof decodedHeader === 'string') {
      throw new Error('Invalid token');
    }

    const kid = decodedHeader.header.kid;
    const key = await this.client.getSigningKey(kid);
    const signingKey = key.getPublicKey();

    const payload: any = jwt.verify(token, signingKey, {
      algorithms: ['RS256'], // Keycloak uses RS256 by default
    });

    // Build LB4 UserProfile
    const userProfile: UserProfile = {
      [securityId]: payload.sub,
      id: payload.sub,
      username: payload.preferred_username,
      email: payload.email,
      roles: payload.realm_access?.roles ?? [],
      ...payload,
    };

    return userProfile;
  }

  private extractToken(request: Request): string | undefined {
    if (!request.headers.authorization) return undefined;
    const authHeader = request.headers.authorization;
    if (!authHeader.startsWith('Bearer ')) return undefined;
    return authHeader.substring(7);
  }
}
