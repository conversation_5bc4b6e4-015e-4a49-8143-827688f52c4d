import {Provider, inject, ValueOrPromise} from '@loopback/core';
import pino, {Logger, LoggerOptions} from 'pino';

export class LoggerProvider implements Provider<Logger> {
  constructor(
    @inject('logger.options', {optional: true})
    private options: LoggerOptions = {},
  ) {}

  value(): ValueOrPromise<Logger> {
    return pino({
      level: process.env.LOG_LEVEL ?? 'info',
      transport:
        process.env.NODE_ENV === 'production'
          ? undefined
          : {
              target: 'pino-pretty',
              options: {
                colorize: true,
                translateTime: 'SYS:standard',
              },
            },
      ...this.options,
    });
  }
}
