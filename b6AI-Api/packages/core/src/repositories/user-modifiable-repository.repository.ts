import {Getter} from '@loopback/core';
import {
  DataObject,
  Options,
  Where,
  Count,
  AnyObject,
} from '@loopback/repository';
import {UserModifiableEntity} from '../models';
import {SoftDeleteBaseRepository} from './soft-delete-repository.repository';

/**
 * Base repository with Soft Delete + Audit (createdBy, modifiedBy).
 *
 * All your repositories for user-modifiable entities should extend this.
 */
export class UserModifiableBaseRepository<
  M extends UserModifiableEntity,
  ID,
  Relations extends object = {},
> extends SoftDeleteBaseRepository<M, ID, Relations> {
  constructor(
    entityClass: typeof UserModifiableEntity & {prototype: M},
    dataSource: any,
    getCurrentUser: Getter<AnyObject | undefined>,
  ) {
    super(entityClass, dataSource, getCurrentUser);
  }

  private async getUserId(): Promise<keyof M | null> {
    const user = await this.getCurrentUser();
    return (user?.id ?? null) as keyof M | null;
  }

  async create(entity: DataObject<M>, options?: Options): Promise<M> {
    const userId = await this.getUserId();
    entity.createdBy = userId as any;
    entity.modifiedBy = userId as any;
    return super.create(entity, options);
  }

  async createAll(
    entities: DataObject<M>[],
    options?: AnyObject,
  ): Promise<M[]> {
    const userId = await this.getUserId();
    const entitiesWithAudit = entities.map(entity => ({
      ...entity,
      createdBy: userId as any,
      modifiedBy: userId as any,
    }));
    return super.createAll(entitiesWithAudit, options);
  }

  async updateById(
    id: ID,
    data: DataObject<M>,
    options?: Options,
  ): Promise<void> {
    const userId = await this.getUserId();
    data.modifiedBy = userId as any;
    return super.updateById(id, data, options);
  }

  async updateAll(
    data: DataObject<M>,
    where?: Where<M>,
    options?: Options,
  ): Promise<Count> {
    const userId = await this.getUserId();
    data.modifiedBy = userId as any;
    return super.updateAll(data, where, options);
  }

  async replaceById(
    id: ID,
    data: DataObject<M>,
    options?: Options,
  ): Promise<void> {
    const userId = await this.getUserId();
    data.modifiedBy = userId as any;
    return super.replaceById(id, data, options);
  }
}
