import {Getter} from '@loopback/core';
import {
  DataObject,
  Options,
  Where,
  Count,
  AnyObject,
  Inclusion,
  Filter,
} from '@loopback/repository';
import {SequelizeCrudRepository} from '@loopback/sequelize';
import {HttpErrors} from '@loopback/rest';
import {SoftDeleteEntity} from '../models';

interface InclusionWithRequired extends Inclusion {
  required?: boolean;
}

type FilterWithRequired<M extends object = AnyObject> = Omit<
  Filter<M>,
  'include'
> & {
  include?: (string | InclusionWithRequired)[];
};

/**
 * Base repository with Soft Delete support.
 *
 * All your repositories should extend this instead of SequelizeCrudRepository.
 */
export class SoftDeleteBaseRepository<
  M extends SoftDeleteEntity,
  ID,
  Relations extends object = {},
> extends SequelizeCrudRepository<M, ID, Relations> {
  constructor(
    entityClass: typeof SoftDeleteEntity & {prototype: M},
    dataSource: any,
    protected readonly getCurrentUser: Getter<AnyObject | undefined>,
  ) {
    super(entityClass, dataSource);
  }

  // ----------------- Helpers -----------------
  private buildWhere(where?: Where<M>): Where<M> {
    if (!where) return {deleted: false} as Where<M>;
    if ('deleted' in where) return where;

    if ('and' in where) {
      return {and: [{deleted: false}, ...(where as any).and]} as Where<M>;
    }
    if ('or' in where) {
      return {and: [{deleted: false}, where]} as Where<M>;
    }
    return {...where, deleted: false} as Where<M>;
  }

  private buildIncludeFilter(
    include?: (string | InclusionWithRequired)[],
  ): (string | InclusionWithRequired)[] | undefined {
    if (!include || include.length === 0) return undefined;
    return include.map(inc => this.processInclude(inc));
  }

  private processInclude(
    inc: string | InclusionWithRequired,
  ): string | InclusionWithRequired {
    if (typeof inc === 'string') {
      return {relation: inc, scope: {where: {deleted: false}}};
    }

    const processed: InclusionWithRequired = {...inc};
    if (!processed.scope) processed.scope = {};
    if (!processed.scope.where) processed.scope.where = {deleted: false};
    else if (!('deleted' in processed.scope.where)) {
      processed.scope.where = {...processed.scope.where, deleted: false};
    }

    if (processed.scope.include?.length) {
      processed.scope.include = this.buildIncludeFilter(
        processed.scope.include as (string | InclusionWithRequired)[],
      );
    }
    return processed;
  }

  private buildSoftDeleteFilter<T extends FilterWithRequired<M>>(
    filter?: T,
  ): T {
    if (!filter) {
      return {where: {deleted: false}} as T;
    }
    return {
      ...filter,
      where: this.buildWhere(filter.where),
      include: this.buildIncludeFilter(filter.include),
    } as T;
  }

  // ----------------- Overridden CRUD methods -----------------
  async delete(entity: M, options?: Options): Promise<void> {
    const user = await this.getCurrentUser();
    await super.updateById(
      (entity as any).id,
      {
        deleted: true,
        deletedAt: new Date(),
        deletedBy: user?.id,
      } as DataObject<M>,
      options,
    );
  }

  async deleteById(id: ID, options?: Options): Promise<void> {
    const user = await this.getCurrentUser();
    await super.updateById(
      id,
      {
        deleted: true,
        deletedAt: new Date(),
        deletedBy: user?.id,
      } as DataObject<M>,
      options,
    );
  }

  async deleteAll(where?: Where<M>, options?: Options): Promise<Count> {
    const user = await this.getCurrentUser();
    return super.updateAll(
      {
        deleted: true,
        deletedAt: new Date(),
        deletedBy: user?.id,
      } as DataObject<M>,
      this.buildWhere(where),
      options,
    );
  }

  async find(
    filter?: FilterWithRequired<M>,
    options?: Options,
  ): Promise<(M & Relations)[]> {
    return super.find(this.buildSoftDeleteFilter(filter) as any, options);
  }

  async findById(
    id: ID,
    filter?: FilterWithRequired<M>,
    options?: Options,
  ): Promise<M & Relations> {
    const actualFilter = filter
      ? {
          ...filter,
          where: this.buildWhere({...filter.where, id} as any),
          include: this.buildIncludeFilter(filter.include),
        }
      : {where: {id, deleted: false}};

    const result = await super.findOne(actualFilter as any, options);
    if (!result) {
      throw new HttpErrors.NotFound(
        `Entity with id ${id} not found or has been deleted`,
      );
    }
    return result;
  }

  async findOne(
    filter?: FilterWithRequired<M>,
    options?: Options,
  ): Promise<(M & Relations) | null> {
    return super.findOne(this.buildSoftDeleteFilter(filter) as any, options);
  }

  async count(where?: Where<M>, options?: Options): Promise<Count> {
    return super.count(this.buildWhere(where), options);
  }

  async updateAll(
    data: DataObject<M>,
    where?: Where<M>,
    options?: Options,
  ): Promise<Count> {
    return super.updateAll(data, this.buildWhere(where), options);
  }

  async updateById(
    id: ID,
    data: DataObject<M>,
    options?: Options,
  ): Promise<void> {
    const exists = await this.exists(id, options);
    if (!exists) {
      throw new HttpErrors.NotFound(
        `Entity with id ${id} not found or has been deleted`,
      );
    }
    const {deleted, ...safeData} = data;
    return super.updateById(id, safeData as DataObject<M>, options);
  }

  async exists(id: ID, options?: Options): Promise<boolean> {
    const count = await this.count({id: id, deleted: false} as any, options);
    return count.count > 0;
  }

  async replaceById(
    id: ID,
    data: DataObject<M>,
    options?: Options,
  ): Promise<void> {
    const exists = await this.exists(id, options);
    if (!exists) {
      throw new HttpErrors.NotFound(
        `Entity with id ${id} not found or has been deleted`,
      );
    }
    return super.replaceById(
      id,
      {...data, deleted: false} as DataObject<M>,
      options,
    );
  }

  // ----------------- Extra Utilities -----------------
  async findDeleted(
    filter?: FilterWithRequired<M>,
    options?: Options,
  ): Promise<(M & Relations)[]> {
    return super.find(
      {...filter, where: {...(filter?.where ?? {}), deleted: true}} as any,
      options,
    );
  }

  async findWithDeleted(
    filter?: FilterWithRequired<M>,
    options?: Options,
  ): Promise<(M & Relations)[]> {
    return super.find(filter as any, options);
  }

  async restore(id: ID, options?: Options): Promise<void> {
    return super.updateById(
      id,
      {deleted: false, deletedAt: null, deletedBy: null} as DataObject<M>,
      options,
    );
  }

  async restoreAll(where?: Where<M>, options?: Options): Promise<Count> {
    return super.updateAll(
      {deleted: false, deletedAt: null, deletedBy: null} as DataObject<M>,
      {...(where ?? {}), deleted: true} as Where<M>,
      options,
    );
  }

  async hardDeleteById(id: ID, options?: Options): Promise<void> {
    return super.deleteById(id, options);
  }

  async hardDeleteAll(where?: Where<M>, options?: Options): Promise<Count> {
    return super.deleteAll(where, options);
  }
}
