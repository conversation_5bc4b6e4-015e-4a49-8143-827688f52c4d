import {
  Request<PERSON>ontex<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Invoke<PERSON>eth<PERSON>,
  Send,
  Reject,
  RestBind<PERSON>,
  Se<PERSON><PERSON><PERSON>ler,
  SequenceA<PERSON>,
  InvokeMiddleware,
} from '@loopback/rest';
import {Logger} from 'pino';
import {LoggerBindings} from './keys';
import {inject} from '@loopback/core';

export class CoreSequence implements Se<PERSON><PERSON><PERSON>ler {
  /**
   * Optional invoker for registered middleware in a chain.
   * To be injected via SequenceActions.INVOKE_MIDDLEWARE.
   */
  @inject(SequenceActions.INVOKE_MIDDLEWARE, {optional: true})
  protected invokeMiddleware: InvokeMiddleware = () => false;

  constructor(
    @inject(LoggerBindings.LOGGER) private logger: Logger,
    @inject(RestBindings.SequenceActions.FIND_ROUTE)
    protected findRoute: FindRoute,
    @inject(RestBindings.SequenceActions.PARSE_PARAMS)
    protected parseParams: ParseParams,
    @inject(RestBindings.SequenceActions.INVOKE_METHOD)
    protected invoke: Invoke<PERSON>eth<PERSON>,
    @inject(RestBindings.SequenceActions.SEND) public send: Send,
    @inject(RestBindings.SequenceActions.REJECT) public reject: Reject,
  ) {}

  async handle(context: RequestContext) {
    const {request, response} = context;
    const start = Date.now();
    this.logger.info(
      {
        method: request.method,
        url: request.url,
        userAgent: request.headers['user-agent'],
      },
      'Incoming Request',
    );

    try {
      const route = this.findRoute(request);
      const finished = await this.invokeMiddleware(context);
      if (finished) return;
      const args = await this.parseParams(request, route);
      const result = await this.invoke(route, args);
      this.send(response, result);

      const duration = Date.now() - start;
      this.logger.info(
        {
          method: request.method,
          url: request.url,
          statusCode: response.statusCode,
          duration,
        },
        'Request completed',
      );
    } catch (err) {
      const duration = Date.now() - start;
      this.logger.error(
        {
          method: request.method,
          url: request.url,
          err,
          duration,
        },
        'Request failed',
      );
      this.reject(context, err);
    }
  }
}
