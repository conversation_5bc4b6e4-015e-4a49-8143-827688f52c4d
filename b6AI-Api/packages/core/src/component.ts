import {
  Application,
  injectable,
  Component,
  config,
  ContextTags,
  CoreBindings,
  inject,
  ProviderMap,
  Binding,
  createBindingFromClass,
} from '@loopback/core';
import {LocalCoreComponentBindings, LoggerBindings} from './keys';
import {DEFAULT__LOCAL_CORE_OPTIONS, LocalCoreComponentOptions} from './types';
import {LoggerProvider} from './providers';

// Configure the binding for LocalCoreComponent
@injectable({tags: {[ContextTags.KEY]: LocalCoreComponentBindings.COMPONENT}})
export class LocalCoreComponent implements Component {
  bindings?: Binding<any>[] | undefined;
  constructor(
    @inject(CoreBindings.APPLICATION_INSTANCE)
    private application: Application,
    @config()
    private options: LocalCoreComponentOptions = DEFAULT__LOCAL_CORE_OPTIONS,
  ) {
    this.bindings = [
      createBindingFromClass(LoggerProvider, {
        key: LoggerBindings.LOGGER,
      }),
    ];
  }
}
