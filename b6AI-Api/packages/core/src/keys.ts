import {BindingKey, CoreBindings} from '@loopback/core';
import {LocalCoreComponent} from './component';
import {Logger} from 'pino';

/**
 * Binding keys used by this component.
 */
export namespace LocalCoreComponentBindings {
  export const COMPONENT = BindingKey.create<LocalCoreComponent>(
    `${CoreBindings.COMPONENTS}.LocalCoreComponent`,
  );
}

export namespace LoggerBindings {
  export const LOGGER = BindingKey.create<Logger>(
    `${CoreBindings.COMPONENTS}.logger`,
  );
}
