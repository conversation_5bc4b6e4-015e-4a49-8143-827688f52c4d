import {Getter, inject} from '@loopback/core';
import {SecurityBindings} from '@loopback/security';
import {BusinessHours, BusinessHoursRelations, BusinessHoursDepartments} from '../models';
import {TenantDataSource} from '../datasources';
import {SoftDeleteBaseRepository} from '@b6ai/core';
import {AnyObject, HasManyRepositoryFactory, repository} from '@loopback/repository';
import {BusinessHoursDepartmentsRepository} from './business-hours-departments.repository';

export class BusinessHoursRepository extends SoftDeleteBaseRepository<
  BusinessHours,
  typeof BusinessHours.prototype.id,
  BusinessHoursRelations
> {
  public readonly businessHoursDepartments: HasManyRepositoryFactory<
    BusinessHoursDepartments,
    typeof BusinessHours.prototype.id
  >;

  constructor(
    @inject('datasources.tenant') dataSource: TenantDataSource,
    @inject.getter(SecurityBindings.USER) getCurrentUser: Getter<AnyObject>,
    @repository.getter('BusinessHoursDepartmentsRepository')
    protected businessHoursDepartmentsRepositoryGetter: Getter<BusinessHoursDepartmentsRepository>,
  ) {
    super(BusinessHours, dataSource, getCurrentUser);
    this.businessHoursDepartments = this.createHasManyRepositoryFactoryFor(
      'businessHoursDepartments',
      businessHoursDepartmentsRepositoryGetter,
    );
    this.registerInclusionResolver(
      'businessHoursDepartments',
      this.businessHoursDepartments.inclusionResolver,
    );
  }
}
