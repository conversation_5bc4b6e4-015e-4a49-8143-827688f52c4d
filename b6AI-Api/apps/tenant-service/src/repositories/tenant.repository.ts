import {Getter, inject} from '@loopback/core';
import {SecurityBindings} from '@loopback/security';
import {Tenant, TenantRelations} from '../models';
import {TenantDataSource} from '../datasources';
import {SoftDeleteBaseRepository} from '@b6ai/core';
import {AnyObject} from '@loopback/repository';

export class TenantRepository extends SoftDeleteBaseRepository<
  Tenant,
  typeof Tenant.prototype.id,
  TenantRelations
> {
  constructor(
    @inject('datasources.tenant') dataSource: TenantDataSource,
    @inject.getter(SecurityBindings.USER) getCurrentUser: Getter<AnyObject>,
  ) {
    super(Tenant, dataSource, getCurrentUser);
  }
}
