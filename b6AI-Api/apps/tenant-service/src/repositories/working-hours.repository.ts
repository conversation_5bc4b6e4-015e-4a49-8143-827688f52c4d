import {Getter, inject} from '@loopback/core';
import {SecurityBindings} from '@loopback/security';
import {
  WorkingHours,
  WorkingHoursRelations,
  WorkingDays,
} from '../models';
import {TenantDataSource} from '../datasources';
import {SoftDeleteBaseRepository} from '@b6ai/core';
import {AnyObject, BelongsToAccessor, repository} from '@loopback/repository';
import {WorkingDaysRepository} from './working-days.repository';

export class WorkingHoursRepository extends SoftDeleteBaseRepository<
  WorkingHours,
  typeof WorkingHours.prototype.id,
  WorkingHoursRelations
> {
  public readonly workingDay: BelongsToAccessor<
    WorkingDays,
    typeof WorkingHours.prototype.id
  >;

  constructor(
    @inject('datasources.tenant') dataSource: TenantDataSource,
    @inject.getter(SecurityBindings.USER) getCurrentUser: Getter<AnyObject>,
    @repository.getter('WorkingDaysRepository')
    protected workingDaysRepositoryGetter: Getter<WorkingDaysRepository>,
  ) {
    super(WorkingHours, dataSource, getCurrentUser);
    
    this.workingDay = this.createBelongsToAccessorFor(
      'workingDay',
      workingDaysRepositoryGetter,
    );
    this.registerInclusionResolver(
      'workingDay',
      this.workingDay.inclusionResolver,
    );
  }
}
