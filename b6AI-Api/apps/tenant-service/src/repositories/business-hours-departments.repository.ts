import {Getter, inject} from '@loopback/core';
import {SecurityBindings} from '@loopback/security';
import {
  BusinessHoursDepartments,
  BusinessHoursDepartmentsRelations,
  BusinessHours,
} from '../models';
import {TenantDataSource} from '../datasources';
import {SoftDeleteBaseRepository} from '@b6ai/core';
import {AnyObject, BelongsToAccessor, repository} from '@loopback/repository';
import {BusinessHoursRepository} from './business-hours.repository';

export class BusinessHoursDepartmentsRepository extends SoftDeleteBaseRepository<
  BusinessHoursDepartments,
  typeof BusinessHoursDepartments.prototype.id,
  BusinessHoursDepartmentsRelations
> {
  public readonly businessHours: BelongsToAccessor<
    BusinessHours,
    typeof BusinessHoursDepartments.prototype.id
  >;

  // Department relationship removed - departments are managed in agent-portal-service

  constructor(
    @inject('datasources.tenant') dataSource: TenantDataSource,
    @inject.getter(SecurityBindings.USER) getCurrentUser: Getter<AnyObject>,
    @repository.getter('BusinessHoursRepository')
    protected businessHoursRepositoryGetter: Getter<BusinessHoursRepository>,
  ) {
    super(BusinessHoursDepartments, dataSource, getCurrentUser);
    this.businessHours = this.createBelongsToAccessorFor(
      'businessHours',
      businessHoursRepositoryGetter,
    );
    this.registerInclusionResolver(
      'businessHours',
      this.businessHours.inclusionResolver,
    );
  }
}
