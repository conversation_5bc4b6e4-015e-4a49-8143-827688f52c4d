import {Getter, inject} from '@loopback/core';
import {SecurityBindings} from '@loopback/security';
import {Holiday, HolidayRelations} from '../models';
import {TenantDataSource} from '../datasources';
import {SoftDeleteBaseRepository} from '@b6ai/core';
import {AnyObject} from '@loopback/repository';

export class HolidayRepository extends SoftDeleteBaseRepository<
  Holiday,
  typeof Holiday.prototype.id,
  HolidayRelations
> {
  constructor(
    @inject('datasources.tenant') dataSource: TenantDataSource,
    @inject.getter(SecurityBindings.USER) getCurrentUser: Getter<AnyObject>,
  ) {
    super(Holiday, dataSource, getCurrentUser);
  }
}
