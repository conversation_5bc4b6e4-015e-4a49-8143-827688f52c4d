import {Getter, inject} from '@loopback/core';
import {SecurityBindings} from '@loopback/security';
import {
  WorkingDays,
  WorkingDaysRelations,
  BusinessHours,
  WorkingHours,
} from '../models';
import {TenantDataSource} from '../datasources';
import {SoftDeleteBaseRepository} from '@b6ai/core';
import {AnyObject, BelongsToAccessor, HasOneRepositoryFactory, repository} from '@loopback/repository';
import {BusinessHoursRepository} from './business-hours.repository';
import {WorkingHoursRepository} from './working-hours.repository';

export class WorkingDaysRepository extends SoftDeleteBaseRepository<
  WorkingDays,
  typeof WorkingDays.prototype.id,
  WorkingDaysRelations
> {
  public readonly businessHours: BelongsToAccessor<
    BusinessHours,
    typeof WorkingDays.prototype.id
  >;

  public readonly workingHours: HasOneRepositoryFactory<
    WorkingHours,
    typeof WorkingDays.prototype.id
  >;

  constructor(
    @inject('datasources.tenant') dataSource: TenantDataSource,
    @inject.getter(SecurityBindings.USER) getCurrentUser: Getter<AnyObject>,
    @repository.getter('BusinessHoursRepository')
    protected businessHoursRepositoryGetter: Getter<BusinessHoursRepository>,
    @repository.getter('WorkingHoursRepository')
    protected workingHoursRepositoryGetter: Getter<WorkingHoursRepository>,
  ) {
    super(WorkingDays, dataSource, getCurrentUser);
    
    this.workingHours = this.createHasOneRepositoryFactoryFor(
      'workingHours',
      workingHoursRepositoryGetter,
    );
    this.registerInclusionResolver(
      'workingHours',
      this.workingHours.inclusionResolver,
    );
    
    this.businessHours = this.createBelongsToAccessorFor(
      'businessHours',
      businessHoursRepositoryGetter,
    );
    this.registerInclusionResolver(
      'businessHours',
      this.businessHours.inclusionResolver,
    );
  }
}
