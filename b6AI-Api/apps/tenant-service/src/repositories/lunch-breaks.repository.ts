import {Getter, inject} from '@loopback/core';
import {SecurityBindings} from '@loopback/security';
import {
  LunchBreaks,
  LunchBreaksRelations,
  BusinessHours,
} from '../models';
import {TenantDataSource} from '../datasources';
import {SoftDeleteBaseRepository} from '@b6ai/core';
import {AnyObject, BelongsToAccessor, repository} from '@loopback/repository';
import {BusinessHoursRepository} from './business-hours.repository';

export class LunchBreaksRepository extends SoftDeleteBaseRepository<
  LunchBreaks,
  typeof LunchBreaks.prototype.id,
  LunchBreaksRelations
> {
  public readonly businessHours: BelongsToAccessor<
    BusinessHours,
    typeof LunchBreaks.prototype.id
  >;

  constructor(
    @inject('datasources.tenant') dataSource: TenantDataSource,
    @inject.getter(SecurityBindings.USER) getCurrentUser: Getter<AnyObject>,
    @repository.getter('BusinessHoursRepository')
    protected businessHoursRepositoryGetter: Getter<BusinessHoursRepository>,
  ) {
    super(LunchBreaks, dataSource, getCurrentUser);
    
    this.businessHours = this.createBelongsToAccessorFor(
      'businessHours',
      businessHoursRepositoryGetter,
    );
    this.registerInclusionResolver(
      'businessHours',
      this.businessHours.inclusionResolver,
    );
  }
}
