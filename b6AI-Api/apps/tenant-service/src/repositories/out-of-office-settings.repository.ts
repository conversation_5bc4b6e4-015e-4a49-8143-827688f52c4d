import {Getter, inject} from '@loopback/core';
import {SecurityBindings} from '@loopback/security';
import {OutOfOfficeSettings, OutOfOfficeSettingsRelations} from '../models';
import {TenantDataSource} from '../datasources';
import {SoftDeleteBaseRepository} from '@b6ai/core';
import {AnyObject} from '@loopback/repository';

export class OutOfOfficeSettingsRepository extends SoftDeleteBaseRepository<
  OutOfOfficeSettings,
  typeof OutOfOfficeSettings.prototype.id,
  OutOfOfficeSettingsRelations
> {
  constructor(
    @inject('datasources.tenant') dataSource: TenantDataSource,
    @inject.getter(SecurityBindings.USER) getCurrentUser: Getter<AnyObject>,
  ) {
    super(OutOfOfficeSettings, dataSource, getCurrentUser);
  }
}
