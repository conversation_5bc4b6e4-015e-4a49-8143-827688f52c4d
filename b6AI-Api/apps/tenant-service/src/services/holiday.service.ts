import {injectable, BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HolidayRepository} from '../repositories';
import {Holiday, HolidayBulkCreateDto, HolidayItem} from '../models';
import {HttpErrors} from '@loopback/rest';

@injectable({scope: BindingScope.TRANSIENT})
export class HolidayService {
  constructor(
    @repository(HolidayRepository)
    public holidayRepository: HolidayRepository,
  ) {}

  async createHoliday(holidayData: Omit<Holiday, 'id'>): Promise<Holiday> {
    // Validate date is not in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (holidayData.date < today) {
      throw new HttpErrors.BadRequest('Holiday date cannot be in the past');
    }

    // Check for duplicate holiday on the same date for the same tenant
    const existingHoliday = await this.holidayRepository.findOne({
      where: {
        tenantId: holidayData.tenantId,
        date: holidayData.date,
      },
    });

    if (existingHoliday) {
      throw new HttpErrors.Conflict(
        `A holiday already exists for ${holidayData.date.toISOString().split('T')[0]}`,
      );
    }

    return this.holidayRepository.create(holidayData);
  }

  async createHolidaysBulk(bulkData: HolidayBulkCreateDto): Promise<Holiday[]> {
    const transaction = await this.holidayRepository.dataSource.beginTransaction();
    
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Validate all dates and check for duplicates
      const holidayDates = bulkData.holidays.map(h => h.date);
      const uniqueDates = new Set(holidayDates.map(d => d.toISOString().split('T')[0]));
      
      if (uniqueDates.size !== holidayDates.length) {
        throw new HttpErrors.BadRequest('Duplicate dates found in the holiday list');
      }

      // Check for past dates
      const pastDates = bulkData.holidays.filter(h => h.date < today);
      if (pastDates.length > 0) {
        throw new HttpErrors.BadRequest(
          `Holiday dates cannot be in the past: ${pastDates.map(h => h.date.toISOString().split('T')[0]).join(', ')}`,
        );
      }

      // Check for existing holidays
      const existingHolidays = await this.holidayRepository.find({
        where: {
          tenantId: bulkData.tenantId,
          date: {inq: holidayDates},
        },
      });

      if (existingHolidays.length > 0) {
        const existingDates = existingHolidays.map(h => h.date.toISOString().split('T')[0]);
        throw new HttpErrors.Conflict(
          `Holidays already exist for the following dates: ${existingDates.join(', ')}`,
        );
      }

      // Create holidays
      const holidaysToCreate = bulkData.holidays.map(holiday => ({
        ...holiday,
        tenantId: bulkData.tenantId,
        status: 'active',
      }));

      const createdHolidays = await this.holidayRepository.createAll(
        holidaysToCreate,
        {transaction},
      );

      await transaction.commit();
      return createdHolidays;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async getHolidaysByTenant(
    tenantId: string,
    year?: number,
    type?: string,
  ): Promise<Holiday[]> {
    const whereClause: any = {tenantId};

    if (year) {
      const startDate = new Date(year, 0, 1);
      const endDate = new Date(year, 11, 31);
      whereClause.date = {between: [startDate, endDate]};
    }

    if (type) {
      whereClause.type = type;
    }

    return this.holidayRepository.find({
      where: whereClause,
      order: ['date ASC'],
    });
  }

  async updateHoliday(
    id: string,
    holidayData: Partial<Holiday>,
  ): Promise<Holiday> {
    const existingHoliday = await this.holidayRepository.findById(id);

    // If date is being updated, validate it
    if (holidayData.date) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (holidayData.date < today) {
        throw new HttpErrors.BadRequest('Holiday date cannot be in the past');
      }

      // Check for duplicate holiday on the new date (excluding current holiday)
      const existingHolidayOnDate = await this.holidayRepository.findOne({
        where: {
          tenantId: existingHoliday.tenantId,
          date: holidayData.date,
          id: {neq: id},
        },
      });

      if (existingHolidayOnDate) {
        throw new HttpErrors.Conflict(
          `A holiday already exists for ${holidayData.date.toISOString().split('T')[0]}`,
        );
      }
    }

    await this.holidayRepository.updateById(id, holidayData);
    return this.holidayRepository.findById(id);
  }

  async deleteHoliday(id: string): Promise<void> {
    await this.holidayRepository.deleteById(id);
  }

  async getUpcomingHolidays(
    tenantId: string,
    daysAhead: number = 30,
  ): Promise<Holiday[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const futureDate = new Date(today);
    futureDate.setDate(futureDate.getDate() + daysAhead);

    return this.holidayRepository.find({
      where: {
        tenantId,
        date: {between: [today, futureDate]},
        status: 'active',
      },
      order: ['date ASC'],
    });
  }
}
