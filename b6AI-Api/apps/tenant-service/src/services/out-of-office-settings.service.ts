import {injectable, BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {OutOfOfficeSettingsRepository} from '../repositories';
import {OutOfOfficeSettings, OutOfOfficeSettingsDto} from '../models';
import {HttpErrors} from '@loopback/rest';

@injectable({scope: BindingScope.TRANSIENT})
export class OutOfOfficeSettingsService {
  constructor(
    @repository(OutOfOfficeSettingsRepository)
    public outOfOfficeSettingsRepository: OutOfOfficeSettingsRepository,
  ) {}

  async createOrUpdateSettings(
    settingsData: OutOfOfficeSettingsDto,
  ): Promise<OutOfOfficeSettings> {
    // Check if settings already exist for this tenant
    const existingSettings = await this.outOfOfficeSettingsRepository.findOne({
      where: {tenantId: settingsData.tenantId},
    });

    if (existingSettings) {
      // Update existing settings
      await this.outOfOfficeSettingsRepository.updateById(
        existingSettings.id!,
        settingsData,
      );
      return this.outOfOfficeSettingsRepository.findById(existingSettings.id!);
    } else {
      // Create new settings
      return this.outOfOfficeSettingsRepository.create(settingsData);
    }
  }

  async getSettingsByTenant(tenantId: string): Promise<OutOfOfficeSettings | null> {
    const settings = await this.outOfOfficeSettingsRepository.findOne({
      where: {tenantId},
    });

    return settings;
  }

  async updateSettings(
    id: string,
    settingsData: Partial<OutOfOfficeSettingsDto>,
  ): Promise<OutOfOfficeSettings> {
    await this.outOfOfficeSettingsRepository.updateById(id, settingsData);
    return this.outOfOfficeSettingsRepository.findById(id);
  }

  async deleteSettings(id: string): Promise<void> {
    await this.outOfOfficeSettingsRepository.deleteById(id);
  }

  async getActiveSettings(tenantId: string): Promise<OutOfOfficeSettings | null> {
    const settings = await this.outOfOfficeSettingsRepository.findOne({
      where: {
        tenantId,
        status: 'active',
      },
    });

    return settings;
  }

  async validateEmergencyContactInfo(emergencyContactInfo: any): Promise<boolean> {
    if (!emergencyContactInfo) {
      return true; // Optional field
    }

    const requiredFields = ['name'];
    const hasAtLeastOneContact = emergencyContactInfo.phone || emergencyContactInfo.email;

    for (const field of requiredFields) {
      if (!emergencyContactInfo[field]) {
        throw new HttpErrors.BadRequest(
          `Emergency contact info must include: ${field}`,
        );
      }
    }

    if (!hasAtLeastOneContact) {
      throw new HttpErrors.BadRequest(
        'Emergency contact info must include at least phone or email',
      );
    }

    return true;
  }

  async createSettings(
    settingsData: OutOfOfficeSettingsDto,
  ): Promise<OutOfOfficeSettings> {
    // Validate emergency contact info if provided
    if (settingsData.emergencyContactInfo) {
      await this.validateEmergencyContactInfo(settingsData.emergencyContactInfo);
    }

    // Check if settings already exist for this tenant
    const existingSettings = await this.outOfOfficeSettingsRepository.findOne({
      where: {tenantId: settingsData.tenantId},
    });

    if (existingSettings) {
      throw new HttpErrors.Conflict(
        'Out of office settings already exist for this tenant. Use update instead.',
      );
    }

    return this.outOfOfficeSettingsRepository.create(settingsData);
  }
}
