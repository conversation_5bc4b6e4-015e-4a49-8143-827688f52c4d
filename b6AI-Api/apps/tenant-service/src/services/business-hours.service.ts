import {injectable, BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
  BusinessHoursRepository,
  BusinessHoursDepartmentsRepository,
  WorkingDaysRepository,
  WorkingHoursRepository,
  LunchBreaksRepository,
} from '../repositories';
import {BusinessHoursCreateDto, BusinessHoursResponseDto} from '../models';

@injectable({scope: BindingScope.TRANSIENT})
export class BusinessHoursService {
  constructor(
    @repository(BusinessHoursRepository)
    public businessHoursRepository: BusinessHoursRepository,
    @repository(BusinessHoursDepartmentsRepository)
    public businessHoursDepartmentsRepository: BusinessHoursDepartmentsRepository,
    @repository(WorkingDaysRepository)
    public workingDaysRepository: WorkingDaysRepository,
    @repository(WorkingHoursRepository)
    public workingHoursRepository: WorkingHoursRepository,
    @repository(LunchBreaksRepository)
    public lunchBreaksRepository: LunchBreaksRepository,
  ) {}

  async createBusinessHours(
    businessHoursData: BusinessHoursCreateDto,
  ): Promise<BusinessHoursResponseDto> {
    const transaction =
      await this.businessHoursRepository.dataSource.beginTransaction();

    try {
      // If this is set as default, ensure no other default exists for this tenant
      if (businessHoursData.isDefault) {
        await this.ensureOnlyOneDefault(
          businessHoursData.tenantId,
          transaction,
        );
      }

      // Note: Department validation is now handled by the facade layer

      // Extract data for different tables
      const {
        departmentIds,
        workingDays,
        workingHours,
        lunchBreak,
        ...businessHoursCreateData
      } = businessHoursData;

      // Create business hours (main record)
      const createdBusinessHours = await this.businessHoursRepository.create(
        businessHoursCreateData,
        {transaction},
      );

      const businessHoursId = createdBusinessHours.id!;

      // Create working days and working hours
      if (workingDays && workingDays.length > 0) {
        for (const dayOfWeek of workingDays) {
          // Create working day
          const workingDay = await this.workingDaysRepository.create(
            {
              businessHoursId,
              dayOfWeek,
              isWorkingDay: true,
            },
            {transaction},
          );

          // Create working hours for this day if provided
          if (workingHours && (workingHours as any)[dayOfWeek]) {
            const dayHours = (workingHours as any)[dayOfWeek] as {
              start: string;
              end: string;
            };
            await this.workingHoursRepository.create(
              {
                workingDayId: workingDay.id!,
                startTime: dayHours.start,
                endTime: dayHours.end,
              },
              {transaction},
            );
          }
        }
      }

      // Create lunch break if provided
      if (lunchBreak) {
        const lunchData = lunchBreak as {start: string; end: string};
        await this.lunchBreaksRepository.create(
          {
            businessHoursId,
            startTime: lunchData.start,
            endTime: lunchData.end,
          },
          {transaction},
        );
      }

      // Create department associations
      if (departmentIds && departmentIds.length > 0) {
        const departmentAssociations = departmentIds.map(departmentId => ({
          businessHoursId,
          departmentId,
        }));

        await this.businessHoursDepartmentsRepository.createAll(
          departmentAssociations,
          {transaction},
        );
      }

      await transaction.commit();

      // Return response with departments
      return this.getBusinessHoursWithDepartments(businessHoursId);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async updateBusinessHours(
    id: string,
    businessHoursData: Partial<BusinessHoursCreateDto>,
  ): Promise<BusinessHoursResponseDto> {
    const transaction =
      await this.businessHoursRepository.dataSource.beginTransaction();

    try {
      const existingBusinessHours =
        await this.businessHoursRepository.findById(id);

      // If this is set as default, ensure no other default exists for this tenant
      if (businessHoursData.isDefault && !existingBusinessHours.isDefault) {
        await this.ensureOnlyOneDefault(
          existingBusinessHours.tenantId,
          transaction,
          id,
        );
      }

      // Extract data for different tables
      const {
        departmentIds,
        workingDays,
        workingHours,
        lunchBreak,
        ...updateData
      } = businessHoursData;

      // Update business hours (main record)
      await this.businessHoursRepository.updateById(id, updateData, {
        transaction,
      });

      // Update working days and working hours if provided
      if (workingDays !== undefined) {
        // Remove existing working days and their hours (cascade will handle working_hours)
        await this.workingDaysRepository.deleteAll(
          {businessHoursId: id},
          {transaction},
        );

        // Create new working days and hours
        if (workingDays.length > 0) {
          for (const dayOfWeek of workingDays) {
            // Create working day
            const workingDay = await this.workingDaysRepository.create(
              {
                businessHoursId: id,
                dayOfWeek,
                isWorkingDay: true,
              },
              {transaction},
            );

            // Create working hours for this day if provided
            if (workingHours && (workingHours as any)[dayOfWeek]) {
              const dayHours = (workingHours as any)[dayOfWeek] as {
                start: string;
                end: string;
              };
              await this.workingHoursRepository.create(
                {
                  workingDayId: workingDay.id!,
                  startTime: dayHours.start,
                  endTime: dayHours.end,
                },
                {transaction},
              );
            }
          }
        }
      }

      // Update lunch break if provided
      if (lunchBreak !== undefined) {
        // Remove existing lunch break
        await this.lunchBreaksRepository.deleteAll(
          {businessHoursId: id},
          {transaction},
        );

        // Create new lunch break if provided
        if (lunchBreak) {
          const lunchData = lunchBreak as {start: string; end: string};
          await this.lunchBreaksRepository.create(
            {
              businessHoursId: id,
              startTime: lunchData.start,
              endTime: lunchData.end,
            },
            {transaction},
          );
        }
      }

      // Update department associations if provided
      if (departmentIds !== undefined) {
        // Note: Department validation is now handled by the facade layer

        // Remove existing associations
        await this.businessHoursDepartmentsRepository.deleteAll(
          {businessHoursId: id},
          {transaction},
        );

        // Create new associations
        if (departmentIds.length > 0) {
          const departmentAssociations = departmentIds.map(departmentId => ({
            businessHoursId: id,
            departmentId,
          }));

          await this.businessHoursDepartmentsRepository.createAll(
            departmentAssociations,
            {transaction},
          );
        }
      }

      await transaction.commit();

      return this.getBusinessHoursWithDepartments(id);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async getBusinessHoursWithDepartments(
    id: string,
  ): Promise<BusinessHoursResponseDto> {
    const businessHours = await this.businessHoursRepository.findById(id);

    // Get working days with their hours
    const workingDaysWithHours = await this.workingDaysRepository.find({
      where: {businessHoursId: id},
      include: ['workingHours'],
    });

    // Get lunch break
    const lunchBreaks = await this.lunchBreaksRepository.find({
      where: {businessHoursId: id},
    });

    // Get associated department IDs (department details will be enriched by facade)
    const departmentAssociations =
      await this.businessHoursDepartmentsRepository.find({
        where: {businessHoursId: id},
      });

    const departments = departmentAssociations.map(assoc => ({
      id: assoc.departmentId,
    }));

    // Convert working days to the expected format
    const workingDays = workingDaysWithHours
      .filter(wd => wd.isWorkingDay)
      .map(wd => wd.dayOfWeek);

    // Convert working hours to the expected format
    const workingHours: {[key: string]: {start: string; end: string}} = {};
    workingDaysWithHours.forEach(wd => {
      if (wd.workingHours && wd.isWorkingDay) {
        workingHours[wd.dayOfWeek.toString()] = {
          start: wd.workingHours.startTime,
          end: wd.workingHours.endTime,
        };
      }
    });

    // Convert lunch break to the expected format
    const lunchBreak =
      lunchBreaks.length > 0
        ? {
            start: lunchBreaks[0].startTime,
            end: lunchBreaks[0].endTime,
          }
        : undefined;

    return new BusinessHoursResponseDto({
      id: businessHours.id,
      tenantId: businessHours.tenantId,
      name: businessHours.name,
      timezone: businessHours.timezone,
      workingDays,
      workingHours,
      lunchBreak,
      isDefault: businessHours.isDefault,
      status: businessHours.status,
      departments,
      createdOn: businessHours.createdOn,
      modifiedOn: businessHours.modifiedOn,
    });
  }

  async getBusinessHoursByTenant(
    tenantId: string,
  ): Promise<BusinessHoursResponseDto[]> {
    const businessHoursList = await this.businessHoursRepository.find({
      where: {tenantId},
    });

    const results: BusinessHoursResponseDto[] = [];
    for (const businessHours of businessHoursList) {
      const result = await this.getBusinessHoursWithDepartments(
        businessHours.id!,
      );
      results.push(result);
    }

    return results;
  }

  private async ensureOnlyOneDefault(
    tenantId: string,
    transaction: any,
    excludeId?: string,
  ): Promise<void> {
    const whereClause: any = {tenantId, isDefault: true};
    if (excludeId) {
      whereClause.id = {neq: excludeId};
    }

    await this.businessHoursRepository.updateAll(
      {isDefault: false},
      whereClause,
      {transaction},
    );
  }

  async deleteBusinessHours(id: string): Promise<void> {
    const transaction =
      await this.businessHoursRepository.dataSource.beginTransaction();

    try {
      // Delete department associations first
      await this.businessHoursDepartmentsRepository.deleteAll(
        {businessHoursId: id},
        {transaction},
      );

      // Delete lunch breaks
      await this.lunchBreaksRepository.deleteAll(
        {businessHoursId: id},
        {transaction},
      );

      // Delete working days (cascade will handle working_hours)
      await this.workingDaysRepository.deleteAll(
        {businessHoursId: id},
        {transaction},
      );

      // Delete business hours
      await this.businessHoursRepository.deleteById(id, {transaction});

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}
