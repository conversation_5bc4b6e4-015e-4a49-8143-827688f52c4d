import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {service} from '@loopback/core';
import {
  BusinessHours,
  BusinessHoursCreateDto,
  BusinessHoursResponseDto,
} from '../models';
import {BusinessHoursRepository} from '../repositories';
import {BusinessHoursService} from '../services';
import {CONTENT_TYPE, STATUS_CODES} from '@b6ai/core';

const basePath = '/business-hours';

export class BusinessHoursController {
  constructor(
    @repository(BusinessHoursRepository)
    public businessHoursRepository: BusinessHoursRepository,
    @service(BusinessHoursService)
    public businessHoursService: BusinessHoursService,
  ) {}

  @post(basePath)
  @response(STATUS_CODES.OK, {
    description: 'BusinessHours model instance',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(BusinessHoursResponseDto)},
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(BusinessHoursCreateDto, {
            title: 'NewBusinessHours',
          }),
        },
      },
    })
    businessHours: BusinessHoursCreateDto,
  ): Promise<BusinessHoursResponseDto> {
    return this.businessHoursService.createBusinessHours(businessHours);
  }

  @get(`${basePath}/count`)
  @response(STATUS_CODES.OK, {
    description: 'BusinessHours model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(BusinessHours) where?: Where<BusinessHours>,
  ): Promise<Count> {
    return this.businessHoursRepository.count(where);
  }

  @get(basePath)
  @response(STATUS_CODES.OK, {
    description: 'Array of BusinessHours model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(BusinessHoursResponseDto, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(BusinessHours) filter?: Filter<BusinessHours>,
  ): Promise<BusinessHours[]> {
    return this.businessHoursRepository.find(filter);
  }

  @get(`${basePath}/tenant/{tenantId}`)
  @response(STATUS_CODES.OK, {
    description: 'Array of BusinessHours model instances for a tenant',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(BusinessHoursResponseDto, {includeRelations: true}),
        },
      },
    },
  })
  async findByTenant(
    @param.path.string('tenantId') tenantId: string,
  ): Promise<BusinessHoursResponseDto[]> {
    return this.businessHoursService.getBusinessHoursByTenant(tenantId);
  }

  @patch(basePath)
  @response(STATUS_CODES.OK, {
    description: 'BusinessHours PATCH success count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(BusinessHours, {partial: true}),
        },
      },
    })
    businessHours: BusinessHours,
    @param.where(BusinessHours) where?: Where<BusinessHours>,
  ): Promise<Count> {
    return this.businessHoursRepository.updateAll(businessHours, where);
  }

  @get(`${basePath}/{id}`)
  @response(STATUS_CODES.OK, {
    description: 'BusinessHours model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(BusinessHoursResponseDto, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(BusinessHours, {exclude: 'where'})
    filter?: FilterExcludingWhere<BusinessHours>,
  ): Promise<BusinessHoursResponseDto> {
    return this.businessHoursService.getBusinessHoursWithDepartments(id);
  }

  @patch(`${basePath}/{id}`)
  @response(STATUS_CODES.OK, {
    description: 'BusinessHours PATCH success',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(BusinessHoursResponseDto)},
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(BusinessHoursCreateDto, {partial: true}),
        },
      },
    })
    businessHours: Partial<BusinessHoursCreateDto>,
  ): Promise<BusinessHoursResponseDto> {
    return this.businessHoursService.updateBusinessHours(id, businessHours);
  }

  @put(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'BusinessHours PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() businessHours: BusinessHours,
  ): Promise<void> {
    await this.businessHoursRepository.replaceById(id, businessHours);
  }

  @del(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'BusinessHours DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.businessHoursService.deleteBusinessHours(id);
  }
}
