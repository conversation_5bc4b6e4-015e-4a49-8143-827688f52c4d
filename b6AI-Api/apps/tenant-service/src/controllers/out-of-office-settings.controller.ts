import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {service} from '@loopback/core';
import {OutOfOfficeSettings, OutOfOfficeSettingsDto} from '../models';
import {OutOfOfficeSettingsRepository} from '../repositories';
import {OutOfOfficeSettingsService} from '../services';
import {CONTENT_TYPE, STATUS_CODES} from '@b6ai/core';

const basePath = '/out-of-office-settings';

export class OutOfOfficeSettingsController {
  constructor(
    @repository(OutOfOfficeSettingsRepository)
    public outOfOfficeSettingsRepository: OutOfOfficeSettingsRepository,
    @service(OutOfOfficeSettingsService)
    public outOfOfficeSettingsService: OutOfOfficeSettingsService,
  ) {}

  @post(basePath)
  @response(STATUS_CODES.OK, {
    description: 'OutOfOfficeSettings model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(OutOfOfficeSettings)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(OutOfOfficeSettingsDto, {
            title: 'NewOutOfOfficeSettings',
          }),
        },
      },
    })
    outOfOfficeSettings: OutOfOfficeSettingsDto,
  ): Promise<OutOfOfficeSettings> {
    return this.outOfOfficeSettingsService.createSettings(outOfOfficeSettings);
  }

  @post(`${basePath}/upsert`)
  @response(STATUS_CODES.OK, {
    description: 'Create or update OutOfOfficeSettings model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(OutOfOfficeSettings)}},
  })
  async createOrUpdate(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(OutOfOfficeSettingsDto, {
            title: 'UpsertOutOfOfficeSettings',
          }),
        },
      },
    })
    outOfOfficeSettings: OutOfOfficeSettingsDto,
  ): Promise<OutOfOfficeSettings> {
    return this.outOfOfficeSettingsService.createOrUpdateSettings(outOfOfficeSettings);
  }

  @get(`${basePath}/count`)
  @response(STATUS_CODES.OK, {
    description: 'OutOfOfficeSettings model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(OutOfOfficeSettings) where?: Where<OutOfOfficeSettings>,
  ): Promise<Count> {
    return this.outOfOfficeSettingsRepository.count(where);
  }

  @get(basePath)
  @response(STATUS_CODES.OK, {
    description: 'Array of OutOfOfficeSettings model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(OutOfOfficeSettings, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(OutOfOfficeSettings) filter?: Filter<OutOfOfficeSettings>,
  ): Promise<OutOfOfficeSettings[]> {
    return this.outOfOfficeSettingsRepository.find(filter);
  }

  @get(`${basePath}/tenant/{tenantId}`)
  @response(STATUS_CODES.OK, {
    description: 'OutOfOfficeSettings model instance for a tenant',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(OutOfOfficeSettings, {includeRelations: true}),
      },
    },
  })
  async findByTenant(
    @param.path.string('tenantId') tenantId: string,
  ): Promise<OutOfOfficeSettings | null> {
    return this.outOfOfficeSettingsService.getSettingsByTenant(tenantId);
  }

  @get(`${basePath}/tenant/{tenantId}/active`)
  @response(STATUS_CODES.OK, {
    description: 'Active OutOfOfficeSettings model instance for a tenant',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(OutOfOfficeSettings, {includeRelations: true}),
      },
    },
  })
  async findActiveByTenant(
    @param.path.string('tenantId') tenantId: string,
  ): Promise<OutOfOfficeSettings | null> {
    return this.outOfOfficeSettingsService.getActiveSettings(tenantId);
  }

  @patch(basePath)
  @response(STATUS_CODES.OK, {
    description: 'OutOfOfficeSettings PATCH success count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(OutOfOfficeSettings, {partial: true}),
        },
      },
    })
    outOfOfficeSettings: OutOfOfficeSettings,
    @param.where(OutOfOfficeSettings) where?: Where<OutOfOfficeSettings>,
  ): Promise<Count> {
    return this.outOfOfficeSettingsRepository.updateAll(outOfOfficeSettings, where);
  }

  @get(`${basePath}/{id}`)
  @response(STATUS_CODES.OK, {
    description: 'OutOfOfficeSettings model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(OutOfOfficeSettings, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(OutOfOfficeSettings, {exclude: 'where'})
    filter?: FilterExcludingWhere<OutOfOfficeSettings>,
  ): Promise<OutOfOfficeSettings> {
    return this.outOfOfficeSettingsRepository.findById(id, filter);
  }

  @patch(`${basePath}/{id}`)
  @response(STATUS_CODES.OK, {
    description: 'OutOfOfficeSettings PATCH success',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(OutOfOfficeSettings)}},
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(OutOfOfficeSettingsDto, {partial: true}),
        },
      },
    })
    outOfOfficeSettings: Partial<OutOfOfficeSettingsDto>,
  ): Promise<OutOfOfficeSettings> {
    return this.outOfOfficeSettingsService.updateSettings(id, outOfOfficeSettings);
  }

  @put(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'OutOfOfficeSettings PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() outOfOfficeSettings: OutOfOfficeSettings,
  ): Promise<void> {
    await this.outOfOfficeSettingsRepository.replaceById(id, outOfOfficeSettings);
  }

  @del(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'OutOfOfficeSettings DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.outOfOfficeSettingsService.deleteSettings(id);
  }
}
