import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {service} from '@loopback/core';
import {Holiday, HolidayBulkCreateDto} from '../models';
import {HolidayRepository} from '../repositories';
import {HolidayService} from '../services';
import {CONTENT_TYPE, STATUS_CODES} from '@b6ai/core';

const basePath = '/holidays';

export class HolidayController {
  constructor(
    @repository(HolidayRepository)
    public holidayRepository: HolidayRepository,
    @service(HolidayService)
    public holidayService: HolidayService,
  ) {}

  @post(basePath)
  @response(STATUS_CODES.OK, {
    description: 'Holiday model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Holiday)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Holiday, {
            title: 'NewHoliday',
            exclude: ['id'],
          }),
        },
      },
    })
    holiday: Omit<Holiday, 'id'>,
  ): Promise<Holiday> {
    return this.holidayService.createHoliday(holiday);
  }

  @post(`${basePath}/bulk`)
  @response(STATUS_CODES.OK, {
    description: 'Bulk create holidays',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Holiday),
        },
      },
    },
  })
  async createBulk(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(HolidayBulkCreateDto),
        },
      },
    })
    bulkData: HolidayBulkCreateDto,
  ): Promise<Holiday[]> {
    return this.holidayService.createHolidaysBulk(bulkData);
  }

  @get(`${basePath}/count`)
  @response(STATUS_CODES.OK, {
    description: 'Holiday model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(@param.where(Holiday) where?: Where<Holiday>): Promise<Count> {
    return this.holidayRepository.count(where);
  }

  @get(basePath)
  @response(STATUS_CODES.OK, {
    description: 'Array of Holiday model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Holiday, {includeRelations: true}),
        },
      },
    },
  })
  async find(@param.filter(Holiday) filter?: Filter<Holiday>): Promise<Holiday[]> {
    return this.holidayRepository.find(filter);
  }

  @get(`${basePath}/tenant/{tenantId}`)
  @response(STATUS_CODES.OK, {
    description: 'Array of Holiday model instances for a tenant',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Holiday, {includeRelations: true}),
        },
      },
    },
  })
  async findByTenant(
    @param.path.string('tenantId') tenantId: string,
    @param.query.number('year') year?: number,
    @param.query.string('type') type?: string,
  ): Promise<Holiday[]> {
    return this.holidayService.getHolidaysByTenant(tenantId, year, type);
  }

  @get(`${basePath}/tenant/{tenantId}/upcoming`)
  @response(STATUS_CODES.OK, {
    description: 'Array of upcoming Holiday model instances for a tenant',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Holiday, {includeRelations: true}),
        },
      },
    },
  })
  async findUpcomingByTenant(
    @param.path.string('tenantId') tenantId: string,
    @param.query.number('daysAhead') daysAhead?: number,
  ): Promise<Holiday[]> {
    return this.holidayService.getUpcomingHolidays(tenantId, daysAhead);
  }

  @patch(basePath)
  @response(STATUS_CODES.OK, {
    description: 'Holiday PATCH success count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Holiday, {partial: true}),
        },
      },
    })
    holiday: Holiday,
    @param.where(Holiday) where?: Where<Holiday>,
  ): Promise<Count> {
    return this.holidayRepository.updateAll(holiday, where);
  }

  @get(`${basePath}/{id}`)
  @response(STATUS_CODES.OK, {
    description: 'Holiday model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Holiday, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Holiday, {exclude: 'where'})
    filter?: FilterExcludingWhere<Holiday>,
  ): Promise<Holiday> {
    return this.holidayRepository.findById(id, filter);
  }

  @patch(`${basePath}/{id}`)
  @response(STATUS_CODES.OK, {
    description: 'Holiday PATCH success',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Holiday)}},
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Holiday, {partial: true}),
        },
      },
    })
    holiday: Holiday,
  ): Promise<Holiday> {
    return this.holidayService.updateHoliday(id, holiday);
  }

  @put(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'Holiday PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() holiday: Holiday,
  ): Promise<void> {
    await this.holidayRepository.replaceById(id, holiday);
  }

  @del(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'Holiday DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.holidayService.deleteHoliday(id);
  }
}
