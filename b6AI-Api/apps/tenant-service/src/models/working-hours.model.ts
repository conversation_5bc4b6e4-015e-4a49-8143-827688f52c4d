import {model, property, belongsTo} from '@loopback/repository';
import {SoftDeleteEntity} from '@b6ai/core';
import {WorkingDays} from './working-days.model';

@model({name: 'working_hours'})
export class WorkingHours extends SoftDeleteEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'start_time',
    jsonSchema: {
      format: 'time',
      description: 'Start time in HH:MM format (e.g., "09:00")',
    },
  })
  startTime: string;

  @property({
    type: 'string',
    required: true,
    name: 'end_time',
    jsonSchema: {
      format: 'time',
      description: 'End time in HH:MM format (e.g., "17:00")',
    },
  })
  endTime: string;

  @belongsTo(
    () => WorkingDays,
    {name: 'workingDay', keyTo: 'id'},
    {name: 'working_day_id'},
  )
  workingDayId: string;

  constructor(data?: Partial<WorkingHours>) {
    super(data);
  }
}

export interface WorkingHoursRelations {
  workingDay?: WorkingDays;
}

export type WorkingHoursWithRelations = WorkingHours & WorkingHoursRelations;
