import {Model, model, property} from '@loopback/repository';

@model()
export class EmergencyContactInfo extends Model {
  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  phone?: string;

  @property({
    type: 'string',
  })
  email?: string;

  @property({
    type: 'string',
  })
  department?: string;

  constructor(data?: Partial<EmergencyContactInfo>) {
    super(data);
  }
}

@model()
export class OutOfOfficeSettingsDto extends Model {
  @property({
    type: 'string',
    required: true,
  })
  tenantId: string;

  @property({
    type: 'string',
  })
  afterHoursMessage?: string;

  @property({
    type: 'boolean',
    default: false,
  })
  enableAfterHoursMessage?: boolean;

  @property({
    type: 'string',
  })
  holidayMessage?: string;

  @property({
    type: 'boolean',
    default: false,
  })
  enableHolidayMessage?: boolean;

  @property({
    type: 'boolean',
    default: false,
  })
  collectContactInfo?: boolean;

  @property({
    type: 'string',
  })
  emergencyContactInfo?: string;

  @property({
    type: 'boolean',
    default: false,
  })
  enableEmergencyContact?: boolean;

  @property({
    type: 'string',
    default: 'active',
  })
  status?: string;

  constructor(data?: Partial<OutOfOfficeSettingsDto>) {
    super(data);
  }
}

export interface OutOfOfficeSettingsDtoRelations {
  // describe navigational properties here
}

export type OutOfOfficeSettingsDtoWithRelations = OutOfOfficeSettingsDto &
  OutOfOfficeSettingsDtoRelations;
