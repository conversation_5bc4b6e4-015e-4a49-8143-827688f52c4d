import {Model, model, property} from '@loopback/repository';

@model()
export class HolidayItem extends Model {
  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'date',
    required: true,
  })
  date: Date;

  @property({
    type: 'string',
    default: 'global',
  })
  type?: string;

  @property({
    type: 'string',
  })
  countryCode?: string;

  constructor(data?: Partial<HolidayItem>) {
    super(data);
  }
}

@model()
export class HolidayBulkCreateDto extends Model {
  @property({
    type: 'string',
    required: true,
  })
  tenantId: string;

  @property({
    type: 'array',
    itemType: HolidayItem,
    required: true,
  })
  holidays: HolidayItem[];

  constructor(data?: Partial<HolidayBulkCreateDto>) {
    super(data);
  }
}

export interface HolidayBulkCreateDtoRelations {
  // describe navigational properties here
}

export type HolidayBulkCreateDtoWithRelations = HolidayBulkCreateDto & HolidayBulkCreateDtoRelations;
