import {model, property, belongsTo, hasOne} from '@loopback/repository';
import {SoftDeleteEntity} from '@b6ai/core';
import {BusinessHours} from './business-hours.model';
import {WorkingHours} from './working-hours.model';

@model({name: 'working_days'})
export class WorkingDays extends SoftDeleteEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'number',
    required: true,
    name: 'day_of_week',
    jsonSchema: {
      minimum: 1,
      maximum: 7,
      description: '1=Monday, 2=Tuesday, ..., 7=Sunday',
    },
  })
  dayOfWeek: number;

  @property({
    type: 'boolean',
    default: true,
    name: 'is_working_day',
    jsonSchema: {
      description: 'Flag to indicate if this day is a working day',
    },
  })
  isWorkingDay?: boolean;

  @belongsTo(
    () => BusinessHours,
    {name: 'businessHours', keyTo: 'id'},
    {name: 'business_hours_id'},
  )
  businessHoursId: string;
  @hasOne(() => WorkingHours, {keyTo: 'workingDayId'})
  workingHours?: WorkingHours;

  constructor(data?: Partial<WorkingDays>) {
    super(data);
  }
}

export interface WorkingDaysRelations {
  businessHours?: BusinessHours;
  workingHours?: WorkingHours;
}

export type WorkingDaysWithRelations = WorkingDays & WorkingDaysRelations;
