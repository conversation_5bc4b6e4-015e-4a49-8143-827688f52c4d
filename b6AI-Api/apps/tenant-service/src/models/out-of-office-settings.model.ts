import {SoftDeleteEntity} from '@b6ai/core';
import {model, property} from '@loopback/repository';

@model({name: 'out_of_office_settings'})
export class OutOfOfficeSettings extends SoftDeleteEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'tenant_id',
  })
  tenantId: string;

  @property({
    type: 'string',
    name: 'after_hours_message',
  })
  afterHoursMessage?: string;

  @property({
    type: 'boolean',
    default: false,
    name: 'enable_after_hours_message',
  })
  enableAfterHoursMessage?: boolean;

  @property({
    type: 'string',
    name: 'holiday_message',
  })
  holidayMessage?: string;

  @property({
    type: 'boolean',
    default: false,
    name: 'enable_holiday_message',
  })
  enableHolidayMessage?: boolean;

  @property({
    type: 'boolean',
    default: false,
    name: 'collect_contact_info',
  })
  collectContactInfo?: boolean;

  @property({
    type: 'string',
    name: 'emergency_contact_info',
    jsonSchema: {
      description: 'Emergency contact information (email or phone number)',
    },
  })
  emergencyContactInfo?: string;

  @property({
    type: 'boolean',
    default: false,
    name: 'enable_emergency_contact',
  })
  enableEmergencyContact?: boolean;

  @property({
    type: 'string',
    default: 'active',
  })
  status?: string;

  constructor(data?: Partial<OutOfOfficeSettings>) {
    super(data);
  }
}

export interface OutOfOfficeSettingsRelations {
  // describe navigational properties here
}

export type OutOfOfficeSettingsWithRelations = OutOfOfficeSettings &
  OutOfOfficeSettingsRelations;
