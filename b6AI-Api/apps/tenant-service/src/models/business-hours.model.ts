import {SoftDeleteEntity} from '@b6ai/core';
import {model, property, hasMany, hasOne} from '@loopback/repository';
import {BusinessHoursDepartments} from './business-hours-departments.model';
import {WorkingDays} from './working-days.model';
import {LunchBreaks} from './lunch-breaks.model';

@model({name: 'business_hours'})
export class BusinessHours extends SoftDeleteEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'tenant_id',
  })
  tenantId: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: true,
  })
  timezone: string;

  // Working days, working hours, and lunch break are now in separate tables

  @property({
    type: 'boolean',
    default: false,
    name: 'is_default',
  })
  isDefault?: boolean;

  @property({
    type: 'string',
    default: 'active',
  })
  status?: string;

  @hasMany(() => BusinessHoursDepartments, {keyTo: 'businessHoursId'})
  businessHoursDepartments: BusinessHoursDepartments[];

  @hasMany(() => WorkingDays, {keyTo: 'businessHoursId'})
  workingDays: WorkingDays[];

  @hasOne(() => LunchBreaks, {keyTo: 'businessHoursId'})
  lunchBreak: LunchBreaks;

  constructor(data?: Partial<BusinessHours>) {
    super(data);
  }
}

export interface BusinessHoursRelations {
  businessHoursDepartments?: BusinessHoursDepartments[];
  workingDays?: WorkingDays[];
  lunchBreak?: LunchBreaks;
}

export type BusinessHoursWithRelations = BusinessHours & BusinessHoursRelations;
