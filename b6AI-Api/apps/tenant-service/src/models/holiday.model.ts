import {SoftDeleteEntity} from '@b6ai/core';
import {model, property} from '@loopback/repository';

@model({name: 'holidays'})
export class Holiday extends SoftDeleteEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'tenant_id',
  })
  tenantId: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'date',
    required: true,
  })
  date: Date;

  @property({
    type: 'string',
    default: 'global',
  })
  type?: string;

  @property({
    type: 'string',
    name: 'country_code',
  })
  countryCode?: string;

  @property({
    type: 'string',
    default: 'active',
  })
  status?: string;

  constructor(data?: Partial<Holiday>) {
    super(data);
  }
}

export interface HolidayRelations {
  // describe navigational properties here
}

export type HolidayWithRelations = Holiday & HolidayRelations;
