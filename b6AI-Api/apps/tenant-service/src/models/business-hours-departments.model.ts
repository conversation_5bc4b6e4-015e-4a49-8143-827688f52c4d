import {model, property, belongsTo} from '@loopback/repository';
import {SoftDeleteEntity} from '@b6ai/core';
import {BusinessHours} from './business-hours.model';

@model({name: 'business_hours_departments'})
export class BusinessHoursDepartments extends SoftDeleteEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'department_id',
  })
  departmentId: string;

  @belongsTo(
    () => BusinessHours,
    {name: 'businessHours', keyTo: 'id'},
    {name: 'business_hours_id'},
  )
  businessHoursId: string;

  constructor(data?: Partial<BusinessHoursDepartments>) {
    super(data);
  }
}

export interface BusinessHoursDepartmentsRelations {
  businessHours?: BusinessHours;
}

export type BusinessHoursDepartmentsWithRelations = BusinessHoursDepartments &
  BusinessHoursDepartmentsRelations;
