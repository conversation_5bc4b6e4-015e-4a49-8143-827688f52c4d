# Business Hours Configuration System

This document describes the implementation of the business hours configuration system for the agent portal in the tenant-service.

## Overview

The business hours configuration system allows tenants to:
1. Configure business hours with timezone, working days, working hours, and lunch breaks
2. Associate business hours with multiple departments
3. Set one configuration as default per tenant
4. Manage holiday schedules (global or country-specific)
5. Configure out-of-office settings with auto-reply messages and emergency contact information

## Database Schema

### Tables Created

1. **business_hours** - Main business hours configuration
2. **business_hours_departments** - Junction table for many-to-many relationship
3. **departments** - Department reference table
4. **holidays** - Holiday schedule management
5. **out_of_office_settings** - Out-of-office auto-reply settings

### Key Features

- **Timezone Support**: IANA timezone format (e.g., 'America/New_York')
- **Working Days**: Array of numbers (1=Monday, 7=Sunday)
- **Working Hours**: JSON object with day-specific hours
- **Lunch Breaks**: Optional JSON object with start/end times
- **Default Configuration**: Only one default per tenant (enforced by unique constraint)
- **Soft Delete**: All entities support soft deletion

## API Endpoints

### Business Hours

- `POST /business-hours` - Create business hours with departments
- `GET /business-hours` - List all business hours
- `GET /business-hours/tenant/{tenantId}` - Get business hours for tenant
- `GET /business-hours/{id}` - Get specific business hours with departments
- `PATCH /business-hours/{id}` - Update business hours
- `DELETE /business-hours/{id}` - Delete business hours

### Holidays

- `POST /holidays` - Create single holiday
- `POST /holidays/bulk` - Create multiple holidays
- `GET /holidays/tenant/{tenantId}` - Get holidays for tenant (with year/type filters)
- `GET /holidays/tenant/{tenantId}/upcoming` - Get upcoming holidays
- `PATCH /holidays/{id}` - Update holiday
- `DELETE /holidays/{id}` - Delete holiday

### Out of Office Settings

- `POST /out-of-office-settings` - Create settings
- `POST /out-of-office-settings/upsert` - Create or update settings
- `GET /out-of-office-settings/tenant/{tenantId}` - Get settings for tenant
- `GET /out-of-office-settings/tenant/{tenantId}/active` - Get active settings
- `PATCH /out-of-office-settings/{id}` - Update settings

### Departments

- `GET /departments` - List all departments
- `GET /departments/active` - List active departments
- `POST /departments` - Create department
- `PATCH /departments/{id}` - Update department

## Data Models

### BusinessHoursCreateDto

```json
{
  "tenantId": "uuid",
  "name": "Main Office Hours",
  "timezone": "America/New_York",
  "workingDays": [1, 2, 3, 4, 5],
  "workingHours": {
    "1": {"start": "09:00", "end": "17:00"},
    "2": {"start": "09:00", "end": "17:00"}
  },
  "lunchBreak": {"start": "12:00", "end": "13:00"},
  "isDefault": true,
  "departmentIds": ["dept-uuid-1", "dept-uuid-2"],
  "status": "active"
}
```

### HolidayBulkCreateDto

```json
{
  "tenantId": "uuid",
  "holidays": [
    {
      "name": "New Year's Day",
      "date": "2024-01-01",
      "type": "global"
    },
    {
      "name": "Independence Day",
      "date": "2024-07-04",
      "type": "country_specific",
      "countryCode": "US"
    }
  ]
}
```

### OutOfOfficeSettingsDto

```json
{
  "tenantId": "uuid",
  "afterHoursMessage": "We're currently closed. Please leave a message.",
  "holidayMessage": "We're closed for the holiday. We'll respond soon.",
  "collectContactInfo": true,
  "emergencyContactInfo": {
    "name": "Emergency Support",
    "phone": "******-0123",
    "email": "<EMAIL>",
    "department": "IT Support"
  }
}
```

## Business Rules

1. **Only one default business hours per tenant** - Enforced by unique constraint and service logic
2. **Holiday dates cannot be in the past** - Validated in service layer
3. **No duplicate holidays on same date per tenant** - Validated in service layer
4. **Department validation** - All department IDs must exist before creating business hours
5. **Emergency contact validation** - Must include name and at least phone or email

## Services

### BusinessHoursService

- Handles complex business logic for creating/updating business hours
- Manages department associations
- Enforces default configuration rules
- Provides transaction support

### HolidayService

- Validates holiday dates and prevents duplicates
- Supports bulk creation with validation
- Provides upcoming holidays functionality

### OutOfOfficeSettingsService

- Manages tenant-specific out-of-office settings
- Supports upsert operations
- Validates emergency contact information

## Migration

Run the migration to create the database tables:

```bash
cd packages/migrations/tenant-service
npm run db:migrate:tenant
```

## Usage Examples

### Creating Business Hours

```typescript
const businessHours = await businessHoursService.createBusinessHours({
  tenantId: 'tenant-123',
  name: 'Standard Business Hours',
  timezone: 'America/New_York',
  workingDays: [1, 2, 3, 4, 5], // Monday to Friday
  workingHours: {
    '1': {start: '09:00', end: '17:00'},
    '2': {start: '09:00', end: '17:00'},
    '3': {start: '09:00', end: '17:00'},
    '4': {start: '09:00', end: '17:00'},
    '5': {start: '09:00', end: '17:00'}
  },
  lunchBreak: {start: '12:00', end: '13:00'},
  isDefault: true,
  departmentIds: ['dept-1', 'dept-2']
});
```

### Creating Holidays in Bulk

```typescript
const holidays = await holidayService.createHolidaysBulk({
  tenantId: 'tenant-123',
  holidays: [
    {name: 'New Year', date: new Date('2024-01-01'), type: 'global'},
    {name: 'Christmas', date: new Date('2024-12-25'), type: 'global'}
  ]
});
```

### Setting Up Out of Office

```typescript
const settings = await outOfOfficeService.createOrUpdateSettings({
  tenantId: 'tenant-123',
  afterHoursMessage: 'We are currently closed. Normal hours are 9 AM - 5 PM EST.',
  holidayMessage: 'We are closed for the holiday. We will respond when we return.',
  collectContactInfo: true,
  emergencyContactInfo: {
    name: 'Emergency Line',
    phone: '******-HELP',
    email: '<EMAIL>'
  }
});
```

## Testing

The system includes comprehensive validation and error handling. Test the APIs using the built-in REST explorer at `/explorer` when the service is running.

## Next Steps

1. Add authentication/authorization to the endpoints
2. Implement timezone conversion utilities
3. Add business hours calculation utilities
4. Create UI components for the agent portal
5. Add notification system for holiday updates
