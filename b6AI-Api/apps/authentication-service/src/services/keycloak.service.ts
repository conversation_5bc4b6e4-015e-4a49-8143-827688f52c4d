import {inject, injectable, BindingScope} from '@loopback/core';
import {keycloakConfig} from '../datasources/configs/keycloak.config';
import {
  Create<PERSON>serData,
  KeycloakUser,
  KeycloakCreateUserRequest,
  LoginRequest,
  KeycloakAuthResponse,
} from '../types/keycloak.types';
import {KeycloakProxy} from './keycloak-proxy.service';

@injectable({scope: BindingScope.TRANSIENT})
export class KeycloakService {
  private accessToken: string | null = null;
  private tokenExpiry: number = 0;
  private logger = console; // Simple logger for now
  private developmentMode: boolean = process.env.DEVELOPMENT_MODE === 'true';

  constructor(
    @inject('services.KeycloakProxy')
    private keycloakProxy: KeycloakProxy,
  ) {}

  private async ensureAuthenticated(): Promise<string> {
    const now = Date.now();

    if (!this.accessToken || now >= this.tokenExpiry) {
      await this.authenticate();
    }

    return this.accessToken!;
  }

  private async authenticate(): Promise<void> {
    try {
      const response = await this.keycloakProxy.login(
        keycloakConfig.clientId,
        keycloakConfig.adminUsername,
        keycloakConfig.adminPassword,
        keycloakConfig.grantType,
      );

      // Parse if it's string
      const data =
        typeof response === 'string' ? JSON.parse(response) : response;

      this.accessToken = data.accessToken;
      this.tokenExpiry = Date.now() + data.expiresIn * 900;

      this.logger.log('✅ Keycloak Admin authenticated');
    } catch (error) {
      this.logger.error('❌ Keycloak authentication failed:', error);
      throw new Error('Failed to authenticate with Keycloak');
    }
  }

  async createUser(userData: CreateUserData): Promise<KeycloakUser> {
    try {
      // Development mode - return simulated user for development
      if (this.developmentMode) {
        const devUser: KeycloakUser = {
          id: `dev-${Date.now()}`,
          username: userData.username,
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          enabled: userData.enabled ?? true,
          emailVerified: false,
          createdTimestamp: Date.now(),
        };
        this.logger.log('✅ Development user created:', devUser.username);
        return devUser;
      }

      const token = await this.ensureAuthenticated();

      const keycloakUserData: KeycloakCreateUserRequest = {
        username: userData.username,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        enabled: userData.enabled ?? true,
      };

      if (userData.password) {
        keycloakUserData.credentials = [
          {
            type: 'password',
            value: userData.password,
            temporary: false,
          },
        ];
      }

      const user = await this.keycloakProxy.createUser(token, keycloakUserData);
      this.logger.log('✅ User created in Keycloak:', user);
      return user;
    } catch (error: any) {
      this.logger.error('❌ Keycloak user creation failed:', error);
      throw error;
    }
  }

  async getUserByUsername(username: string): Promise<KeycloakUser | null> {
    try {
      const token = await this.ensureAuthenticated();
      const users = await this.keycloakProxy.getUserByUsername(token, username);

      if (!users || users.length === 0) {
        return null;
      }

      this.logger.log('✅ User retrieved from Keycloak by username:', username);
      return users[0];
    } catch (error: any) {
      this.logger.error(
        '❌ Keycloak user retrieval by username failed:',
        error,
      );
      throw error;
    }
  }

  async loginUser(
    loginData: LoginRequest,
  ): Promise<{authResponse: KeycloakAuthResponse; user: KeycloakUser}> {
    try {
      // Development mode - return simulated response for development
      if (this.developmentMode) {
        const devUser: KeycloakUser = {
          id: `dev-${Date.now()}`,
          username: loginData.username,
          email: loginData.username,
          firstName: 'Development',
          lastName: 'User',
          enabled: true,
          emailVerified: false,
          createdTimestamp: Date.now(),
        };

        const devAuthResponse: KeycloakAuthResponse = {
          accessToken: 'dev-access-token-' + Date.now(),
          refreshToken: 'dev-refresh-token-' + Date.now(),
          expiresIn: 3600,
          refreshExpiresIn: 7200,
          tokenType: 'Bearer',
          'not-before-policy': 0,
          sessionState: 'dev-session-state',
          scope: 'openid profile email',
        };

        this.logger.log('✅ Development user logged in:', loginData.username);
        return {authResponse: devAuthResponse, user: devUser};
      }

      // First, authenticate the user to get their token
      const authResponse = await this.keycloakProxy.login(
        keycloakConfig.clientId,
        loginData.username,
        loginData.password,
        keycloakConfig.grantType,
      );

      // Parse if it's string
      const tokenData: KeycloakAuthResponse =
        typeof authResponse === 'string'
          ? JSON.parse(authResponse)
          : authResponse;

      // Get user details using the user's token or admin token
      const user = await this.getUserByUsername(loginData.username);

      if (!user) {
        throw new Error('User not found after successful authentication');
      }

      this.logger.log('✅ User logged in successfully:', loginData.username);
      return {authResponse: tokenData, user};
    } catch (error: any) {
      this.logger.error('❌ Keycloak user login failed:', error);
      throw new Error('Invalid username or password');
    }
  }
}
