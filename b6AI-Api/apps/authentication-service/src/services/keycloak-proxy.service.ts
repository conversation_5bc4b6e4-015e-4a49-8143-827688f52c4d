import {inject, Provider} from '@loopback/core';
import {getService} from '@loopback/service-proxy';
import {KeycloakDataSource} from '../datasources';
import {
  KeycloakAuthResponse,
  KeycloakCreateUserRequest,
  KeycloakUpdateUserRequest,
  KeycloakUser,
} from '../types';

export interface KeycloakProxy {
  login(
    clientId: string,
    username: string,
    password: string,
    grantType: string,
  ): Promise<KeycloakAuthResponse>;

  createUser(
    accessToken: string,
    data: KeycloakCreateUserRequest,
  ): Promise<KeycloakUser>;
  getUserById(accessToken: string, id: string): Promise<KeycloakUser>;
  updateUser(
    accessToken: string,
    id: string,
    data: KeycloakUpdateUserRequest,
  ): Promise<void>;
  deleteUser(accessToken: string, id: string): Promise<void>;
  resetPassword(accessToken: string, id: string, data: any): Promise<void>;
  getUserByUsername(
    accessToken: string,
    username: string,
  ): Promise<KeycloakUser[]>;
}

export class KeycloakProxyProvider implements Provider<KeycloakProxy> {
  constructor(
    // keycloak must match the name property in the datasource json file
    @inject('datasources.keycloak')
    protected dataSource: KeycloakDataSource = new KeycloakDataSource(),
  ) {}

  value(): Promise<KeycloakProxy> {
    return getService(this.dataSource);
  }
}
