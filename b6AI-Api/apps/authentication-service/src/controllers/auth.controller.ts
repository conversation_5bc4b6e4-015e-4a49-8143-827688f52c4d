import {post, requestBody, response, getModelSchemaRef} from '@loopback/rest';
import {inject} from '@loopback/core';
import {KeycloakService} from '../services';
import {
  CreateUserRequestDto,
  LoginRequestDto,
  LoginResponseDto,
  KeycloakResponseDto,
} from '../models';

// Constants for status codes and content types
const STATUS_CODES = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  INTERNAL_SERVER_ERROR: 500,
};

const CONTENT_TYPE = {
  JSON: 'application/json',
};

const basePath = '/auth';

export class AuthController {
  constructor(
    @inject('services.KeycloakService')
    private keycloakService: KeycloakService,
  ) {}

  @post(`${basePath}/users`)
  @response(STATUS_CODES.CREATED, {
    description: 'User created successfully',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(KeycloakResponseDto)},
    },
  })
  async createUser(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(CreateUserRequestDto, {
            title: 'CreateUser',
          }),
        },
      },
    })
    userData: CreateUserRequestDto,
  ): Promise<KeycloakResponseDto> {
    const user = await this.keycloakService.createUser(userData);
    return new KeycloakResponseDto(user);
  }

  @post(`${basePath}/login`)
  @response(STATUS_CODES.OK, {
    description: 'User logged in successfully',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(LoginResponseDto)},
    },
  })
  async login(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(LoginRequestDto, {
            title: 'Login',
          }),
        },
      },
    })
    loginData: LoginRequestDto,
  ): Promise<LoginResponseDto> {
    const {authResponse, user} =
      await this.keycloakService.loginUser(loginData);
    return new LoginResponseDto({
      message: 'Login successful',
      accessToken: authResponse.accessToken,
      refreshToken: authResponse.refreshToken,
      expiresIn: authResponse.expiresIn,
      tokenType: authResponse.tokenType,
      user: new KeycloakResponseDto(user),
    });
  }
}
