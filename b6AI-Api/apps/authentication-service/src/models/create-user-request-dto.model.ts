import {Model, model, property} from '@loopback/repository';

@model()
export class CreateUserRequestDto extends Model {
  @property({
    type: 'string',
    required: true,
    description: 'Username for the user',
  })
  username: string;

  @property({
    type: 'string',
    required: true,
    format: 'email',
    description: 'Email address of the user',
  })
  email: string;

  @property({
    type: 'string',
    required: true,
    description: 'First name of the user',
  })
  firstName: string;

  @property({
    type: 'string',
    required: true,
    description: 'Last name of the user',
  })
  lastName: string;

  @property({
    type: 'string',
    description: 'Password for the user (optional, will be auto-generated if not provided)',
  })
  password?: string;

  @property({
    type: 'boolean',
    default: true,
    description: 'Whether the user account is enabled',
  })
  enabled?: boolean;

  constructor(data?: Partial<CreateUserRequestDto>) {
    super(data);
  }
}

export interface CreateUserRequestDtoRelations {
  // describe navigational properties here
}

export type CreateUserRequestDtoWithRelations = CreateUserRequestDto & CreateUserRequestDtoRelations;
