import {Model, model, property} from '@loopback/repository';
import {KeycloakResponseDto} from './keycloak-response-dto.model';

@model()
export class LoginResponseDto extends Model {
  @property({
    type: 'string',
    required: true,
    description: 'Response message',
  })
  message: string;

  @property({
    type: 'string',
    required: true,
    description: 'Access token',
  })
  accessToken: string;

  @property({
    type: 'string',
    required: true,
    description: 'Refresh token',
  })
  refreshToken: string;

  @property({
    type: 'number',
    required: true,
    description: 'Token expiration time in seconds',
  })
  expiresIn: number;

  @property({
    type: 'string',
    required: true,
    description: 'Token type',
  })
  tokenType: string;

  @property({
    type: 'object',
    required: true,
    description: 'User information',
  })
  user: KeycloakResponseDto;

  constructor(data?: Partial<LoginResponseDto>) {
    super(data);
  }
}

export interface LoginResponseDtoRelations {
  // describe navigational properties here
}

export type LoginResponseDtoWithRelations = LoginResponseDto &
  LoginResponseDtoRelations;
