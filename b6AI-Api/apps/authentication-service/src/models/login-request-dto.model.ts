import {Model, model, property} from '@loopback/repository';

@model()
export class LoginRequestDto extends Model {
  @property({
    type: 'string',
    required: true,
    description: 'Username or email for login',
  })
  username: string;

  @property({
    type: 'string',
    required: true,
    description: 'Password for login',
  })
  password: string;

  constructor(data?: Partial<LoginRequestDto>) {
    super(data);
  }
}

export interface LoginRequestDtoRelations {
  // describe navigational properties here
}

export type LoginRequestDtoWithRelations = LoginRequestDto & LoginRequestDtoRelations;
