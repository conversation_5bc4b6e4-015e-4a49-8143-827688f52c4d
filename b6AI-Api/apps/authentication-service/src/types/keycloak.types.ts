export interface CreateUserData {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  password?: string;
  enabled?: boolean;
}

export interface UpdateUserData {
  id: string;
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  enabled?: boolean;
}

export interface KeycloakUser {
  id?: string;
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  enabled?: boolean;
  createdTimestamp?: number;
  emailVerified?: boolean;
}

export interface KeycloakAuthResponse {
  accessToken: string;
  expiresIn: number;
  refreshExpiresIn: number;
  refreshToken: string;
  tokenType: string;
  'not-before-policy': number;
  sessionState: string;
  scope: string;
}

export interface KeycloakCredential {
  type: string;
  value: string;
  temporary: boolean;
}

export interface KeycloakCreateUserRequest {
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  enabled: boolean;
  credentials?: KeycloakCredential[];
}

export interface KeycloakUpdateUserRequest {
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  enabled?: boolean;
}

export interface PasswordResetRequest {
  newPassword: string;
  temporary?: boolean;
}

export interface AuthServiceResponse<T = any> {
  message: string;
  data?: T;
  user?: T;
}

// API Request/Response interfaces for REST endpoints
export interface CreateUserRequest {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  password?: string;
  enabled?: boolean;
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  enabled?: boolean;
}

export interface UserResponse {
  message: string;
  user: KeycloakUser;
}

export interface UsersResponse {
  message: string;
  users: KeycloakUser[];
}

export interface MessageResponse {
  message: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  message: string;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
  user: KeycloakUser;
}
