export interface KeycloakConfig {
  baseUrl: string;
  realm: string;
  adminUsername: string;
  adminPassword: string;
  clientId: string;
  grantType: string;
}

export const keycloakConfig: KeycloakConfig = {
  baseUrl: process.env.KEYCLOAK_BASE_URL || 'http://localhost:8080',
  realm: process.env.KEYCLOAK_REALM_NAME || 'master',
  adminUsername: process.env.KC_BOOTSTRAP_ADMIN_USERNAME || 'admin',
  adminPassword: process.env.KC_BOOTSTRAP_ADMIN_PASSWORD || 'admin',
  clientId: process.env.KEYCLOAK_CLIENT_ID || 'admin-cli',
  grantType: 'password',
};

export const keycloakEndpoints = {
  auth: `${process.env.KEYCLOAK_BASE_URL}/realms/master/protocol/openid-connect/token`,
  users: '/admin/realms/{realm}/users',
  userById: '/admin/realms/{realm}/users/{id}',
  resetPassword: '/admin/realms/{realm}/users/{id}/reset-password',
};

export const keycloakDataSourceConfig = {
  name: 'keycloak',
  connector: 'rest',
  baseURL: keycloakConfig.baseUrl,
  crud: false,
  options: {
    headers: {
      'Content-Type': 'application/json',
    },
  },
  operations: [
    {
      template: {
        method: 'POST',
        url: keycloakEndpoints.auth,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        form: {
          clientId: '{client_id}',
          username: '{username}',
          password: '{password}',
          grantType: '{grant_type}',
        },
      },
      functions: {
        login: ['client_id', 'username', 'password', 'grant_type'],
      },
    },
    {
      template: {
        method: 'POST',
        url: keycloakEndpoints.users,
        headers: {
          Authorization: 'Bearer {access_token}',
          'Content-Type': 'application/json',
        },
        json: '{data}',
      },
      functions: {
        createUser: ['access_token', 'data'],
      },
    },
    {
      template: {
        method: 'GET',
        url: keycloakEndpoints.userById,
        headers: {
          Authorization: 'Bearer {access_token}',
        },
      },
      functions: {
        getUserById: ['access_token', 'id'],
      },
    },
    {
      template: {
        method: 'PUT',
        url: keycloakEndpoints.userById,
        headers: {
          Authorization: 'Bearer {access_token}',
          'Content-Type': 'application/json',
        },
        json: '{data}',
      },
      functions: {
        updateUser: ['access_token', 'id', 'data'],
      },
    },
    {
      template: {
        method: 'DELETE',
        url: keycloakEndpoints.userById,
        headers: {
          Authorization: 'Bearer {access_token}',
        },
      },
      functions: {
        deleteUser: ['access_token', 'id'],
      },
    },
    {
      template: {
        method: 'PUT',
        url: keycloakEndpoints.resetPassword,
        headers: {
          Authorization: 'Bearer {access_token}',
          'Content-Type': 'application/json',
        },
        json: '{data}',
      },
      functions: {
        resetPassword: ['access_token', 'id', 'data'],
      },
    },
    {
      template: {
        method: 'GET',
        url: keycloakEndpoints.users,
        headers: {
          Authorization: 'Bearer {access_token}',
        },
        query: {
          username: '{username}',
          exact: 'true',
        },
      },
      functions: {
        getUserByUsername: ['access_token', 'username'],
      },
    },
  ],
};
