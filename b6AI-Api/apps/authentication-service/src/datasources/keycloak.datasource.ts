import {juggler} from '@loopback/repository';
import {inject, lifeCycleObserver, LifeCycleObserver} from '@loopback/core';
import {keycloakDataSourceConfig} from './configs/keycloak.config';

@lifeCycleObserver('datasource')
export class KeycloakDataSource extends juggler.DataSource implements LifeCycleObserver {
  static dataSourceName = 'keycloak';
  static readonly defaultConfig = keycloakDataSourceConfig;

  constructor(
    @inject('datasources.config.keycloak', {optional: true})
    dsConfig: object = keycloakDataSourceConfig,
  ) {
    super(dsConfig);
  }
}
