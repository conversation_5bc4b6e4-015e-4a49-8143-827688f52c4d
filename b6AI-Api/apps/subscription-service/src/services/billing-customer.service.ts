import {injectable, /* inject, */ BindingScope, service} from '@loopback/core';
import {BillingCustomer} from '../models';
import {ChargebeeService} from './chargebee.service';
import {repository} from '@loopback/repository';
import {BillingCustomerRepository} from '../repositories';
import {GATEWAY_NAME} from '../enums';

@injectable({scope: BindingScope.TRANSIENT})
export class BillingCustomerService {
  constructor(
    @service(ChargebeeService)
    private readonly chargebeeService: ChargebeeService,
    @repository(BillingCustomerRepository)
    private readonly billingCustomerRepository: BillingCustomerRepository,
  ) {}

  async createCustomer(
    customer: Omit<BillingCustomer, 'id'>,
  ): Promise<BillingCustomer> {
    const chargebeeId = await this.chargebeeService.createCustomer(customer);
    return this.billingCustomerRepository.create({
      ...customer,
      gatewayName: GATEWAY_NAME.CHARGEBEE,
      gatewayRefId: chargebeeId,
    });
  }
}
