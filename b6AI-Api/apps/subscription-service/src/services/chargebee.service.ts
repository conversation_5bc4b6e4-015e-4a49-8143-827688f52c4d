import {injectable, BindingScope} from '@loopback/core';
import Chargebee from 'chargebee';
import {
  Billing<PERSON>ustomer,
  CheckoutDto,
  CheckoutResponseDto,
  PlanDto,
} from '../models';
import {TRIAL_PERIOD, TRIAL_PERIOD_UNIT} from '../constants';
import {BILLING_PERIOD} from '@b6ai/core';

@injectable({scope: BindingScope.TRANSIENT})
export class ChargebeeService {
  chargebee: Chargebee;
  constructor() {
    this.chargebee = new Chargebee({
      apiKey: process.env.CHARGEBEE_API_KEY ?? '',
      site: process.env.CHARGEBEE_SITE ?? '',
    });
  }

  async createChargebeePlan(
    plan: Omit<PlanDto, 'gatewayRefId' | 'gatewayName'>,
  ): Promise<string> {
    const chargebeePlan = await this.chargebee.item.create({
      name: plan.name,
      id: plan.id,
      item_family_id: process.env.CHARGEBEE_PLAN_FAMILY ?? '',
      description: plan.description,
      type: 'plan',
      enabled_for_checkout: true,
    });

    await Promise.all(
      [BILLING_PERIOD.MONTHLY, BILLING_PERIOD.YEARLY].map(period =>
        this.createChargebeeItemPrice(
          `${plan.name} ${period}`,
          `${plan.id}_${period.toLowerCase()}`,
          chargebeePlan.item.id,
          plan.currency,
          period === BILLING_PERIOD.MONTHLY
            ? plan.priceCents
            : plan.yearPriceCents,
          period === BILLING_PERIOD.MONTHLY ? 'month' : 'year',
        ),
      ),
    );

    return chargebeePlan.item.id;
  }

  private async createChargebeeItemPrice(
    planName: string,
    planId: string,
    cPlanId: string,
    currency: string,
    price: number,
    periodUnit: 'day' | 'week' | 'month' | 'year',
  ): Promise<void> {
    await this.chargebee.itemPrice.create({
      id: `${planId}`,
      name: planName,
      description: `${planName} Price`,
      item_id: cPlanId,
      currency_code: currency,
      is_taxable: true,
      pricing_model: 'per_unit',
      price: price,
      period_unit: periodUnit,
      period: 1,
      trial_period: TRIAL_PERIOD,
      trial_period_unit: TRIAL_PERIOD_UNIT,
    });
  }

  async createCustomer(customer: Omit<BillingCustomer, 'id'>): Promise<string> {
    const chargebeeCustomer = await this.chargebee.customer.create({
      first_name: customer.firstName,
      last_name: customer.lastName,
      email: customer.email,
      company: customer.company,
    });

    return chargebeeCustomer.customer.id;
  }

  async checkout(
    checkout: Omit<CheckoutDto, 'id' | 'gatewayRefId' | 'gatewayName'>,
  ): Promise<CheckoutResponseDto> {
    const checkoutRes = await this.chargebee.hostedPage.checkoutNewForItems({
      layout: 'full_page',
      billing_cycles: 1,
      customer: {
        first_name: checkout.firstName,
        last_name: checkout.lastName,
        email: checkout.email,
        company: checkout.company,
        id: checkout.tenantId,
      },
      redirect_url: `${process.env.ENTERPRISE_WEB_URL}`,
      subscription: {
        trial_end: Math.floor(Date.now() / 1000) + TRIAL_PERIOD * 24 * 60 * 60,
      },
      subscription_items: [
        {
          item_type: 'plan',
          item_price_id: `${checkout.planId}_${checkout.period.toLowerCase()}`,
          quantity: 1,
        },
      ],
    });
    return new CheckoutResponseDto({
      hostedPageUrl: checkoutRes.hosted_page.url,
    });
  }
}
