import {injectable, /* inject, */ BindingScope, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
  FeatureRepository,
  PlanFeatureRepository,
  PlanRepository,
} from '../repositories';
import {Plan, PlanDto} from '../models';
import {ChargebeeService} from './chargebee.service';
import {GATEWAY_NAME} from '../enums';

@injectable({scope: BindingScope.TRANSIENT})
export class PlanService {
  constructor(
    @repository(PlanRepository) public planRepository: PlanRepository,
    @repository(PlanFeatureRepository)
    public planFeatureRepository: PlanFeatureRepository,
    @repository(FeatureRepository) public featureRepository: FeatureRepository,
    @service(ChargebeeService) public chargebeeService: ChargebeeService,
  ) {}

  async createPlan(
    plan: Omit<PlanDto, 'id' | 'gatewayRefId' | 'gatewayName'>,
  ): Promise<Plan> {
    const transaction = await this.planRepository.dataSource.beginTransaction();
    try {
      const {features, ...planData} = plan;
      const chargebeePlanId = await this.chargebeeService.createChargebeePlan({
        ...plan,
        id: plan.planCode.toUpperCase(),
      });
      const createdPlan = await this.planRepository.create(
        {
          ...planData,
          gatewayName: GATEWAY_NAME.CHARGEBEE,
          gatewayRefId: chargebeePlanId,
        },
        {
          transaction,
        },
      );

      await this.planFeatureRepository.createAll(
        features.map(feature => ({
          ...feature,
          planId: createdPlan.id,
        })),
        {transaction},
      );
      await this.planRepository.updateById(
        createdPlan.id,
        {
          gatewayRefId: chargebeePlanId,
        },
        {transaction},
      );
      await transaction.commit();
      return createdPlan;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}
