import {Getter, inject} from '@loopback/core';
import {AnyObject} from '@loopback/repository';
import {PgDataSource} from '../datasources';
import {BillingCustomer, BillingCustomerRelations} from '../models';
import {SoftDeleteBaseRepository} from '@b6ai/core';
import {SecurityBindings} from '@loopback/security';

export class BillingCustomerRepository extends SoftDeleteBaseRepository<
  BillingCustomer,
  typeof BillingCustomer.prototype.id,
  BillingCustomerRelations
> {
  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(SecurityBindings.USER) getCurrentUser: Getter<AnyObject>,
  ) {
    super(BillingCustomer, dataSource, getCurrentUser);
  }
}
