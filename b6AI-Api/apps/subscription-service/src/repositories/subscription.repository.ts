import {Getter, inject} from '@loopback/core';
import {AnyObject, DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {PgDataSource} from '../datasources';
import {Subscription, SubscriptionRelations, Plan} from '../models';
import {SoftDeleteBaseRepository} from '@b6ai/core';
import {SecurityBindings} from '@loopback/security';
import {PlanRepository} from './plan.repository';

export class SubscriptionRepository extends SoftDeleteBaseRepository<
  Subscription,
  typeof Subscription.prototype.id,
  SubscriptionRelations
> {

  public readonly plan: BelongsToAccessor<Plan, typeof Subscription.prototype.id>;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(SecurityBindings.USER) getCurrentUser: Getter<AnyObject>, @repository.getter('PlanRepository') protected planRepositoryGetter: Getter<PlanRepository>,
  ) {
    super(Subscription, dataSource, getCurrentUser);
    this.plan = this.createBelongsToAccessorFor('plan', planRepositoryGetter,);
    this.registerInclusionResolver('plan', this.plan.inclusionResolver);
  }
}
