import {Getter, inject} from '@loopback/core';
import {PgDataSource} from '../datasources';
import {PlanFeature, PlanFeatureRelations, Feature, Plan} from '../models';
import {SoftDeleteBaseRepository} from '@b6ai/core';
import {SecurityBindings} from '@loopback/security';
import {AnyObject, repository, BelongsToAccessor} from '@loopback/repository';
import {FeatureRepository} from './feature.repository';
import {PlanRepository} from './plan.repository';

export class PlanFeatureRepository extends SoftDeleteBaseRepository<
  PlanFeature,
  typeof PlanFeature.prototype.id,
  PlanFeatureRelations
> {
  public readonly feature: BelongsToAccessor<
    Feature,
    typeof PlanFeature.prototype.id
  >;

  public readonly plan: BelongsToAccessor<
    Plan,
    typeof PlanFeature.prototype.id
  >;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(SecurityBindings.USER) getCurrentUser: Getter<AnyObject>,
    @repository.getter('FeatureRepository')
    protected featureRepositoryGetter: Getter<FeatureRepository>,
    @repository.getter('PlanRepository')
    protected planRepositoryGetter: Getter<PlanRepository>,
  ) {
    super(PlanFeature, dataSource, getCurrentUser);
    this.plan = this.createBelongsToAccessorFor('plan', planRepositoryGetter);
    this.registerInclusionResolver('plan', this.plan.inclusionResolver);
    this.feature = this.createBelongsToAccessorFor(
      'feature',
      featureRepositoryGetter,
    );
    this.registerInclusionResolver('feature', this.feature.inclusionResolver);
  }
}
