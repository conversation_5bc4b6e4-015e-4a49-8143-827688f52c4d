import {Getter, inject} from '@loopback/core';
import {PgDataSource} from '../datasources';
import {Plan, PlanRelations, PlanFeature} from '../models';
import {SecurityBindings} from '@loopback/security';
import {
  AnyObject,
  repository,
  HasManyRepositoryFactory,
} from '@loopback/repository';
import {SoftDeleteBaseRepository} from '@b6ai/core';
import {PlanFeatureRepository} from './plan-feature.repository';

export class PlanRepository extends SoftDeleteBaseRepository<
  Plan,
  typeof Plan.prototype.id,
  PlanRelations
> {
  public readonly planFeatures: HasManyRepositoryFactory<
    PlanFeature,
    typeof Plan.prototype.id
  >;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(SecurityBindings.USER) getCurrentUser: Getter<AnyObject>,
    @repository.getter('PlanFeatureRepository')
    protected planFeatureRepositoryGetter: Getter<PlanFeatureRepository>,
  ) {
    super(Plan, dataSource, getCurrentUser);
    this.planFeatures = this.createHasManyRepositoryFactoryFor(
      'planFeatures',
      planFeatureRepositoryGetter,
    );
    this.registerInclusionResolver(
      'planFeatures',
      this.planFeatures.inclusionResolver,
    );
  }
}
