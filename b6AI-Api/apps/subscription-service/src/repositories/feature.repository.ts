import {Getter, inject} from '@loopback/core';
import {PgDataSource} from '../datasources';
import {Feature, FeatureRelations} from '../models';
import {SoftDeleteBaseRepository} from '@b6ai/core';
import {SecurityBindings} from '@loopback/security';
import {AnyObject} from '@loopback/repository';

export class FeatureRepository extends SoftDeleteBaseRepository<
  Feature,
  typeof Feature.prototype.id,
  FeatureRelations
> {
  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(SecurityBindings.USER) getCurrentUser: Getter<AnyObject>,
  ) {
    super(Feature, dataSource, getCurrentUser);
  }
}
