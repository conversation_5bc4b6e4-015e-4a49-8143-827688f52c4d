import {model, property} from '@loopback/repository';
import {Plan} from './plan.model';
import {PlanFeature} from './plan-feature.model';

@model()
export class PlanDto extends Plan {
  @property({
    type: 'array',
    itemType: 'object',
    required: true,
  })
  features: Pick<PlanFeature, 'featureId' | 'value'>[];

  constructor(data?: Partial<PlanDto>) {
    super(data);
  }
}

export interface PlanDtoRelations {
  // describe navigational properties here
}

export type PlanDtoWithRelations = PlanDto & PlanDtoRelations;
