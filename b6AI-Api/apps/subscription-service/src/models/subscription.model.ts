import {PERIOD_UNIT, SoftDeleteEntity} from '@b6ai/core';
import {model, property, belongsTo} from '@loopback/repository';
import {Plan} from './plan.model';

@model({name: 'subscriptions'})
export class Subscription extends SoftDeleteEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
    name: 'id',
  })
  id: string;

  @property({
    type: 'string',
    required: true,
    name: 'tenant_id',
  })
  tenantId: string;

  @property({
    type: 'string',
    required: true,
    name: 'status',
  })
  status: string;

  @property({
    type: 'date',
    required: true,
    name: 'start_date',
  })
  startDate: string;

  @property({
    type: 'date',
    name: 'end_date',
  })
  endDate: string;

  @property({
    type: 'date',
    name: 'trial_end_date',
  })
  trialEndDate?: string;

  @property({
    type: 'date',
    name: 'renewal_date',
  })
  renewalDate?: string;

  @property({
    type: 'number',
    default: 1,
    name: 'billing_period',
  })
  billingPeriod: number;

  @property({
    type: 'string',
    default: 'month',
    name: 'billing_period_unit',
  })
  billingPeriodUnit: PERIOD_UNIT;

  @property({
    type: 'string',
    required: true,
    name: 'gateway_name',
  })
  gatewayName: string;

  @property({
    type: 'string',
    required: true,
    name: 'gateway_ref_id',
  })
  gatewayRefId: string;

  @belongsTo(() => Plan, {keyTo: 'id'}, {name: 'plan_id'})
  planId: string;

  constructor(data?: Partial<Subscription>) {
    super(data);
  }
}

export interface SubscriptionRelations {
  // describe navigational properties here
}

export type SubscriptionWithRelations = Subscription & SubscriptionRelations;
