import {SoftDeleteEntity} from '@b6ai/core';
import {model, property, hasMany} from '@loopback/repository';
import {PlanFeature} from './plan-feature.model';

@model({
  name: 'plans',
})
export class Plan extends SoftDeleteEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
    name: 'plan_code',
    jsonSchema: {
      maxLength: 50,
      errorMessage: 'Plan code is required and must be <= 50 chars',
    },
  })
  planCode: string;

  @property({
    type: 'string',
    required: true,
    name: 'name',
    jsonSchema: {
      maxLength: 100,
      errorMessage: 'Plan name is required and must be <= 100 chars',
    },
  })
  name: string;

  @property({
    type: 'string',
    name: 'description',
  })
  description?: string;

  @property({
    type: 'number',
    required: true,
    name: 'price_cents',
    jsonSchema: {
      minimum: 0,
      errorMessage: 'Price must be a positive integer (in cents)',
    },
  })
  priceCents: number;

  @property({
    type: 'number',
    required: true,
    name: 'year_price_cents',
    jsonSchema: {
      minimum: 0,
      errorMessage: 'Price must be a positive integer (in cents)',
    },
  })
  yearPriceCents: number;

  @property({
    type: 'string',
    required: true,
    default: 'USD',
    name: 'currency',
    jsonSchema: {
      maxLength: 10,
      errorMessage: 'Currency code must be <= 10 chars',
    },
  })
  currency: string;

  @property({
    type: 'string',
    required: true,
    name: 'gateway_name',
    jsonSchema: {
      maxLength: 50,
      errorMessage: 'Gateway name must be <= 50 chars',
    },
  })
  gatewayName: string;

  @property({
    type: 'string',
    required: true,
    name: 'gateway_ref_id',
    jsonSchema: {
      maxLength: 100,
      errorMessage: 'Gateway reference ID must be <= 100 chars',
    },
  })
  gatewayRefId: string;

  @hasMany(() => PlanFeature, {keyTo: 'planId'})
  planFeatures: PlanFeature[];

  constructor(data?: Partial<Plan>) {
    super(data);
  }
}

export interface PlanRelations {
  // describe navigational properties here
}

export type PlanWithRelations = Plan & PlanRelations;
