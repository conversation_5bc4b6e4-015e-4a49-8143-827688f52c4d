import {model, property} from '@loopback/repository';
import {BillingCustomer} from '.';
import {BILLING_PERIOD} from '@b6ai/core';

@model()
export class CheckoutDto extends BillingCustomer {
  @property({
    type: 'string',
    required: true,
  })
  planId: string;

  @property({
    type: 'string',
    required: true,
  })
  period: BILLING_PERIOD;

  constructor(data?: Partial<CheckoutDto>) {
    super(data);
  }
}

export interface CheckoutDtoRelations {
  // describe navigational properties here
}

export type CheckoutDtoWithRelations = CheckoutDto & CheckoutDtoRelations;
