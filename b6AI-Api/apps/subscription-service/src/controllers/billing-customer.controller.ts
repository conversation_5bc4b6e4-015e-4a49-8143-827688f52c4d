import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {BillingCustomer} from '../models';
import {BillingCustomerRepository} from '../repositories';
import {CONTENT_TYPE, STATUS_CODES, STRATEGY} from '@b6ai/core';
import {BillingCustomerService} from '../services';
import {service} from '@loopback/core';
import {authenticate} from '@loopback/authentication';

const basePath = '/billing-customers';

@authenticate(STRATEGY.KEYCLOCK)
export class BillingCustomerController {
  constructor(
    @repository(BillingCustomerRepository)
    public billingCustomerRepository: BillingCustomerRepository,
    @service(BillingCustomerService)
    private readonly billingCustomerService: BillingCustomerService,
  ) {}

  @post(basePath)
  @response(STATUS_CODES.OK, {
    description: 'BillingCustomer model instance',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(BillingCustomer)},
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(BillingCustomer, {
            title: 'NewBillingCustomer',
            exclude: ['id'],
          }),
        },
      },
    })
    billingCustomer: Omit<BillingCustomer, 'id'>,
  ): Promise<BillingCustomer> {
    return this.billingCustomerService.createCustomer(billingCustomer);
  }

  @get(`${basePath}/count`)
  @response(STATUS_CODES.OK, {
    description: 'BillingCustomer model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(BillingCustomer) where?: Where<BillingCustomer>,
  ): Promise<Count> {
    return this.billingCustomerRepository.count(where);
  }

  @get(basePath)
  @response(STATUS_CODES.OK, {
    description: 'Array of BillingCustomer model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(BillingCustomer, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(BillingCustomer) filter?: Filter<BillingCustomer>,
  ): Promise<BillingCustomer[]> {
    return this.billingCustomerRepository.find(filter);
  }

  @patch(basePath)
  @response(STATUS_CODES.OK, {
    description: 'BillingCustomer PATCH success count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(BillingCustomer, {partial: true}),
        },
      },
    })
    billingCustomer: BillingCustomer,
    @param.where(BillingCustomer) where?: Where<BillingCustomer>,
  ): Promise<Count> {
    return this.billingCustomerRepository.updateAll(billingCustomer, where);
  }

  @get(`${basePath}/{id}`)
  @response(STATUS_CODES.OK, {
    description: 'BillingCustomer model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(BillingCustomer, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(BillingCustomer, {exclude: 'where'})
    filter?: FilterExcludingWhere<BillingCustomer>,
  ): Promise<BillingCustomer> {
    return this.billingCustomerRepository.findById(id, filter);
  }

  @patch(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'BillingCustomer PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(BillingCustomer, {partial: true}),
        },
      },
    })
    billingCustomer: BillingCustomer,
  ): Promise<void> {
    await this.billingCustomerRepository.updateById(id, billingCustomer);
  }

  @put(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'BillingCustomer PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() billingCustomer: BillingCustomer,
  ): Promise<void> {
    await this.billingCustomerRepository.replaceById(id, billingCustomer);
  }

  @del(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'BillingCustomer DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.billingCustomerRepository.deleteById(id);
  }
}
