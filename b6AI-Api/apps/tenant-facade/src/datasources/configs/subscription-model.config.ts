import {ModifiedRestService} from '@sourceloop/core';
import {CheckoutDto, CheckoutResponseDto, Subscription} from '../../models';

export type SubscriptionProxyType = {
  checkout(
    body: Omit<CheckoutDto, 'id' | 'gatewayRefId' | 'gatewayName'>,
    token: string,
  ): Promise<CheckoutResponseDto>;
} & ModifiedRestService<Subscription>;

export const subscriptionProxyConfig = [
  {
    template: {
      method: 'POST',
      url: '/subscriptions/checkout',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      checkout: ['body', 'token'],
    },
  },
];
