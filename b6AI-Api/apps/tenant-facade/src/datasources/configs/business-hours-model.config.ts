import {ModifiedRestService} from '@sourceloop/core';

// Business Hours Models (copied from tenant-service)
export interface BusinessHours {
  id?: string;
  tenantId: string;
  name: string;
  timezone: string;
  workingDays: number[];
  workingHours: object;
  lunchBreak?: object;
  isDefault?: boolean;
  status?: string;
  createdOn?: string;
  modifiedOn?: string;
}

export interface BusinessHoursCreateDto {
  tenantId: string;
  name: string;
  timezone: string;
  workingDays: number[];
  workingHours: object;
  lunchBreak?: object;
  isDefault?: boolean;
  departmentIds: string[];
  status?: string;
}

export interface BusinessHoursResponseDto {
  id?: string;
  tenantId: string;
  name: string;
  timezone: string;
  workingDays: number[];
  workingHours: object;
  lunchBreak?: object;
  isDefault?: boolean;
  status?: string;
  departments?: any[];
  createdOn?: string;
  modifiedOn?: string;
}

export interface Holiday {
  id?: string;
  tenantId: string;
  name: string;
  date: Date;
  type?: string;
  countryCode?: string;
  status?: string;
  createdOn?: string;
  modifiedOn?: string;
}

export interface HolidayBulkCreateDto {
  tenantId: string;
  holidays: Array<{
    name: string;
    date: Date;
    type?: string;
    countryCode?: string;
  }>;
}

export interface OutOfOfficeSettings {
  id?: string;
  tenantId: string;
  afterHoursMessage?: string;
  holidayMessage?: string;
  collectContactInfo?: boolean;
  emergencyContactInfo?: object;
  status?: string;
  createdOn?: string;
  modifiedOn?: string;
}

export interface OutOfOfficeSettingsDto {
  tenantId: string;
  afterHoursMessage?: string;
  holidayMessage?: string;
  collectContactInfo?: boolean;
  emergencyContactInfo?: object;
  status?: string;
}

// Proxy Types
export interface BusinessHoursProxyType {
  createBusinessHours(
    body: BusinessHoursCreateDto,
    token: string,
  ): Promise<BusinessHoursResponseDto>;
  updateBusinessHours(
    id: string,
    body: Partial<BusinessHoursCreateDto>,
    token: string,
  ): Promise<BusinessHoursResponseDto>;
  getBusinessHoursByTenant(
    tenantId: string,
    token: string,
  ): Promise<BusinessHoursResponseDto[]>;
  getBusinessHoursById(
    id: string,
    token: string,
  ): Promise<BusinessHoursResponseDto>;
  deleteBusinessHours(id: string, token: string): Promise<void>;
}

export interface HolidayProxyType {
  createHoliday(body: Omit<Holiday, 'id'>, token: string): Promise<Holiday>;
  createHolidaysBulk(
    body: HolidayBulkCreateDto,
    token: string,
  ): Promise<Holiday[]>;
  getHolidaysByTenant(
    tenantId: string,
    token: string,
    year?: number,
    type?: string,
  ): Promise<Holiday[]>;
  getUpcomingHolidays(
    tenantId: string,
    token: string,
    daysAhead?: number,
  ): Promise<Holiday[]>;
  updateHoliday(
    id: string,
    body: Partial<Holiday>,
    token: string,
  ): Promise<Holiday>;
  deleteHoliday(id: string, token: string): Promise<void>;
}

export interface OutOfOfficeSettingsProxyType {
  createOrUpdateSettings(
    body: OutOfOfficeSettingsDto,
    token: string,
  ): Promise<OutOfOfficeSettings>;
  getSettingsByTenant(
    tenantId: string,
    token: string,
  ): Promise<OutOfOfficeSettings | null>;
  getActiveSettings(
    tenantId: string,
    token: string,
  ): Promise<OutOfOfficeSettings | null>;
  updateSettings(
    id: string,
    body: Partial<OutOfOfficeSettingsDto>,
    token: string,
  ): Promise<OutOfOfficeSettings>;
  deleteSettings(id: string, token: string): Promise<void>;
}

// Proxy Configurations
export const businessHoursProxyConfig = [
  {
    template: {
      method: 'POST',
      url: '/business-hours',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      createBusinessHours: ['body', 'token'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: '/business-hours/{id}',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      updateBusinessHours: ['id', 'body', 'token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/business-hours/tenant/{tenantId}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      getBusinessHoursByTenant: ['tenantId', 'token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/business-hours/{id}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      getBusinessHoursById: ['id', 'token'],
    },
  },
  {
    template: {
      method: 'DELETE',
      url: '/business-hours/{id}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      deleteBusinessHours: ['id', 'token'],
    },
  },
];

export const holidayProxyConfig = [
  {
    template: {
      method: 'POST',
      url: '/holidays',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      createHoliday: ['body', 'token'],
    },
  },
  {
    template: {
      method: 'POST',
      url: '/holidays/bulk',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      createHolidaysBulk: ['body', 'token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/holidays/tenant/{tenantId}?year={year}&type={type}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      getHolidaysByTenant: ['tenantId', 'token', 'year', 'type'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/holidays/tenant/{tenantId}/upcoming?daysAhead={daysAhead}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      getUpcomingHolidays: ['tenantId', 'token', 'daysAhead'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: '/holidays/{id}',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      updateHoliday: ['id', 'body', 'token'],
    },
  },
  {
    template: {
      method: 'DELETE',
      url: '/holidays/{id}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      deleteHoliday: ['id', 'token'],
    },
  },
];

export const outOfOfficeSettingsProxyConfig = [
  {
    template: {
      method: 'POST',
      url: '/out-of-office-settings/upsert',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      createOrUpdateSettings: ['body', 'token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/out-of-office-settings/tenant/{tenantId}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      getSettingsByTenant: ['tenantId', 'token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/out-of-office-settings/tenant/{tenantId}/active',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      getActiveSettings: ['tenantId', 'token'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: '/out-of-office-settings/{id}',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      updateSettings: ['id', 'body', 'token'],
    },
  },
  {
    template: {
      method: 'DELETE',
      url: '/out-of-office-settings/{id}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      deleteSettings: ['id', 'token'],
    },
  },
];
