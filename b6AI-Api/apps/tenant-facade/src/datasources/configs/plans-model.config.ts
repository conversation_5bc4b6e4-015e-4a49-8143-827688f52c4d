import {ModifiedRestService} from '@sourceloop/core';
import {Plan} from '../../models';
import {PlanDto} from '../../models/plan-dto.model';

export type PlanProxyType = {
  createPlan(
    body: Omit<PlanDto, 'id' | 'gatewayRefId' | 'gatewayName'>,
    token: string,
  ): Promise<Plan>;
} & ModifiedRestService<Plan>;

export const planProxyConfig = [
  {
    template: {
      method: 'POST',
      url: '/plans',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      createPlan: ['body', 'token'],
    },
  },
];
