import {inject, lifeCycleObserver, LifeCycleObserver} from '@loopback/core';
import {juggler} from '@loopback/repository';

const config = {
  name: 'authService',
  connector: 'rest',
  baseUrl: process.env.AUTH_SERVICE_URL || 'http://localhost:4000', // Changed from baseURL to baseUrl
  crud: false,
  debug: true, // Enable debug logging
  options: {
    headers: {
      'Content-Type': 'application/json',
    },
  },
  operations: [
    {
      template: {
        method: 'POST',
        url: '/auth/users',
        headers: {
          'Content-Type': 'application/json',
        },
        json: '{data}',
      },
      functions: {
        createUser: ['data'],
      },
    },
    {
      template: {
        method: 'POST',
        url: '/auth/login',
        headers: {
          'Content-Type': 'application/json',
        },
        json: '{data}',
      },
      functions: {
        login: ['data'],
      },
    },
  ],
};

@lifeCycleObserver('datasource')
export class AuthServiceDataSource
  extends juggler.DataSource
  implements LifeCycleObserver
{
  static dataSourceName = 'authService';
  static readonly defaultConfig = config;

  constructor(
    @inject('datasources.config.authService', {optional: true})
    dsConfig: object = config,
  ) {
    super(dsConfig);
  }
}
