import {OnboardTenantDto, OnboardResponseDto} from '../models';
import {service} from '@loopback/core';
import {getModelSchemaRef, post, requestBody, response} from '@loopback/rest';
import {TenantService} from '../services';
import {CONTENT_TYPE, STATUS_CODES} from '@b6ai/core';

const basePath = '/tenants';
export class TenantController {
  constructor(
    @service(TenantService)
    private readonly tenantService: TenantService,
  ) {}

  @post(`${basePath}/onboard`)
  @response(STATUS_CODES.OK, {
    description: 'Tenant onboarded successfully',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(OnboardResponseDto)},
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(OnboardTenantDto, {
            title: 'NewTenant',
          }),
        },
      },
    })
    tenant: OnboardTenantDto,
  ): Promise<OnboardResponseDto> {
    return this.tenantService.onboard(tenant);
  }
}
