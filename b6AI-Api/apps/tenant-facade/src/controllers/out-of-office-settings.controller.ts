import {service} from '@loopback/core';
import {
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {authenticate} from '@loopback/authentication';
import {STRATEGY, CONTENT_TYPE, STATUS_CODES} from '@b6ai/core';
import {OutOfOfficeSettings, OutOfOfficeSettingsDto} from '../models';
import {OutOfOfficeFacadeService} from '../services';

const basePath = '/out-of-office-settings';

@authenticate(STRATEGY.KEYCLOCK)
export class OutOfOfficeSettingsController {
  constructor(
    @service(OutOfOfficeFacadeService)
    private readonly outOfOfficeFacadeService: OutOfOfficeFacadeService,
  ) {}

  @post(`${basePath}/upsert`)
  @response(STATUS_CODES.OK, {
    description: 'Out-of-office settings created or updated successfully',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(OutOfOfficeSettings),
      },
    },
  })
  async createOrUpdate(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(OutOfOfficeSettingsDto),
        },
      },
    })
    settings: OutOfOfficeSettingsDto,
  ): Promise<OutOfOfficeSettings> {
    const token = 'test-token'; // TODO: Extract from request headers
    return this.outOfOfficeFacadeService.createOrUpdateSettings(settings, token);
  }

  @get(`${basePath}/tenant/{tenantId}`)
  @response(STATUS_CODES.OK, {
    description: 'Out-of-office settings for tenant',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(OutOfOfficeSettings),
      },
    },
  })
  async findByTenant(
    @param.path.string('tenantId') tenantId: string,
  ): Promise<OutOfOfficeSettings | null> {
    const token = 'test-token'; // TODO: Extract from request headers
    return this.outOfOfficeFacadeService.getSettingsByTenant(tenantId, token);
  }

  @get(`${basePath}/tenant/{tenantId}/active`)
  @response(STATUS_CODES.OK, {
    description: 'Active out-of-office settings for tenant',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(OutOfOfficeSettings),
      },
    },
  })
  async findActive(
    @param.path.string('tenantId') tenantId: string,
  ): Promise<OutOfOfficeSettings | null> {
    const token = 'test-token'; // TODO: Extract from request headers
    return this.outOfOfficeFacadeService.getActiveSettings(tenantId, token);
  }

  @patch(`${basePath}/{id}`)
  @response(STATUS_CODES.OK, {
    description: 'Out-of-office settings updated successfully',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(OutOfOfficeSettings),
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(OutOfOfficeSettingsDto, {partial: true}),
        },
      },
    })
    settings: Partial<OutOfOfficeSettingsDto>,
  ): Promise<OutOfOfficeSettings> {
    const token = 'test-token'; // TODO: Extract from request headers
    return this.outOfOfficeFacadeService.updateSettings(id, settings, token);
  }

  @del(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'Out-of-office settings deleted successfully',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    const token = 'test-token'; // TODO: Extract from request headers
    await this.outOfOfficeFacadeService.deleteSettings(id, token);
  }
}
