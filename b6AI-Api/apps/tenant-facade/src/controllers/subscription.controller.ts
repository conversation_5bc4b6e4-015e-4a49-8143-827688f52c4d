import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {CheckoutDto, CheckoutResponseDto, Subscription} from '../models';
import {CONTENT_TYPE, STATUS_CODES} from '@b6ai/core';
import {restService} from '@sourceloop/core';
import {SubscriptionProxyType} from '../datasources/configs';

const basePath = '/subscriptions';

export class SubscriptionController {
  constructor(
    @restService(Subscription)
    public subscriptionProxy: SubscriptionProxyType,
  ) {}

  @post(basePath)
  @response(STATUS_CODES.OK, {
    description: 'Subscription model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Subscription)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Subscription, {
            title: 'NewSubscription',
            exclude: ['id'],
          }),
        },
      },
    })
    subscription: Omit<Subscription, 'id'>,
  ): Promise<Subscription> {
    return this.subscriptionProxy.create(subscription);
  }

  @get(`${basePath}/count`)
  @response(STATUS_CODES.OK, {
    description: 'Subscription model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(Subscription) where?: Where<Subscription>,
  ): Promise<Count> {
    return this.subscriptionProxy.count(where);
  }

  @get(basePath)
  @response(STATUS_CODES.OK, {
    description: 'Array of Subscription model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Subscription, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Subscription) filter?: Filter<Subscription>,
  ): Promise<Subscription[]> {
    return this.subscriptionProxy.find(filter);
  }

  @get(`${basePath}/{id}`)
  @response(STATUS_CODES.OK, {
    description: 'Subscription model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Subscription, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Subscription, {exclude: 'where'})
    filter?: FilterExcludingWhere<Subscription>,
  ): Promise<Subscription> {
    return this.subscriptionProxy.findById(id, filter);
  }

  @patch(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'Subscription PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Subscription, {partial: true}),
        },
      },
    })
    subscription: Subscription,
  ): Promise<void> {
    await this.subscriptionProxy.updateById(id, subscription);
  }

  @put(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'Subscription PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() subscription: Subscription,
  ): Promise<void> {
    await this.subscriptionProxy.replaceById(id, subscription);
  }

  @del(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'Subscription DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.subscriptionProxy.deleteById(id);
  }

  @post(`${basePath}/checkout`)
  @response(STATUS_CODES.OK, {
    description: 'Subscription model instance',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(CheckoutResponseDto)},
    },
  })
  async checkout(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(CheckoutDto, {
            title: 'Checkout',
            exclude: ['id', 'gatewayRefId', 'gatewayName'],
          }),
        },
      },
    })
    checkout: Omit<CheckoutDto, 'id' | 'gatewayRefId' | 'gatewayName'>,
  ): Promise<CheckoutResponseDto> {
    return this.subscriptionProxy.checkout(checkout, 'test-token');
  }
}
