import {CONTENT_TYPE, STATUS_CODES} from '@b6ai/core';
import {post, requestBody, response} from '@loopback/rest';
import {SubscriptionEvent} from '../models';
import {service} from '@loopback/core';
import {WebhookService} from '../services';

const basePath = 'webhooks';
export class WebhookController {
  constructor(
    @service(WebhookService)
    private readonly webhokService: WebhookService,
  ) {}

  @post(`${basePath}/chargebee/subscription`)
  @response(STATUS_CODES.OK, {
    description: 'Plan model instance',
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: {
            type: 'object',
          },
        },
      },
    })
    event: SubscriptionEvent,
  ): Promise<void> {
    return this.webhokService.handleSubscriptionEvent(event);
  }
}
