import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Plan} from '../models';
import {authenticate} from '@loopback/authentication';
import {CONTENT_TYPE, STATUS_CODES, STRATEGY} from '@b6ai/core';
import {restService} from '@sourceloop/core';
import {PlanProxyType} from '../datasources/configs';
import {PlanDto} from '../models/plan-dto.model';

const basePath = '/plans';
@authenticate(STRATEGY.KEYCLOCK)
export class PlanController {
  constructor(
    @restService(Plan)
    public planProxy: PlanProxyType,
  ) {}

  @post(basePath)
  @response(STATUS_CODES.OK, {
    description: 'Plan model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Plan)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(PlanDto, {
            title: 'NewPlan',
            exclude: ['id', 'gatewayRefId', 'gatewayName'],
          }),
        },
      },
    })
    plan: Omit<PlanDto, 'id' | 'gatewayRefId' | 'gatewayName'>,
  ): Promise<Plan> {
    return this.planProxy.createPlan(plan, 'test-token');
  }

  @get(`${basePath}/count`)
  @response(STATUS_CODES.OK, {
    description: 'Plan model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(@param.where(Plan) where?: Where<Plan>): Promise<Count> {
    return this.planProxy.count(where);
  }

  @get(basePath)
  @response(STATUS_CODES.OK, {
    description: 'Array of Plan model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Plan, {includeRelations: true}),
        },
      },
    },
  })
  async find(@param.filter(Plan) filter?: Filter<Plan>): Promise<Plan[]> {
    return this.planProxy.find(filter);
  }

  @get(`${basePath}/{id}`)
  @response(STATUS_CODES.OK, {
    description: 'Plan model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Plan, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Plan, {exclude: 'where'}) filter?: FilterExcludingWhere<Plan>,
  ): Promise<Plan> {
    return this.planProxy.findById(id, filter);
  }

  @patch(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'Plan PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Plan, {partial: true}),
        },
      },
    })
    plan: Plan,
  ): Promise<void> {
    await this.planProxy.updateById(id, plan);
  }

  @put(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'Plan PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() plan: Plan,
  ): Promise<void> {
    await this.planProxy.replaceById(id, plan);
  }

  @del(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'Plan DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.planProxy.deleteById(id);
  }
}
