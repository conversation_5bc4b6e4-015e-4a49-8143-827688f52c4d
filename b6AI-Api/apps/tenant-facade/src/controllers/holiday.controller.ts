import {service} from '@loopback/core';
import {
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {authenticate} from '@loopback/authentication';
import {STRATEGY, CONTENT_TYPE, STATUS_CODES} from '@b6ai/core';
import {Holiday, HolidayBulkCreateDto} from '../models';
import {HolidayFacadeService} from '../services';

const basePath = '/holidays';

@authenticate(STRATEGY.KEYCLOCK)
export class HolidayController {
  constructor(
    @service(HolidayFacadeService)
    private readonly holidayFacadeService: HolidayFacadeService,
  ) {}

  @post(basePath)
  @response(STATUS_CODES.OK, {
    description: 'Holiday created successfully',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Holiday),
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Holiday, {
            title: 'NewHoliday',
            exclude: ['id'],
          }),
        },
      },
    })
    holiday: Omit<Holiday, 'id'>,
  ): Promise<Holiday> {
    const token = 'test-token'; // TODO: Extract from request headers
    return this.holidayFacadeService.createHoliday(holiday, token);
  }

  @post(`${basePath}/bulk`)
  @response(STATUS_CODES.OK, {
    description: 'Holidays created in bulk successfully',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Holiday),
        },
      },
    },
  })
  async createBulk(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(HolidayBulkCreateDto),
        },
      },
    })
    bulkData: HolidayBulkCreateDto,
  ): Promise<Holiday[]> {
    const token = 'test-token'; // TODO: Extract from request headers
    return this.holidayFacadeService.createHolidaysBulk(bulkData, token);
  }

  @get(`${basePath}/tenant/{tenantId}`)
  @response(STATUS_CODES.OK, {
    description: 'Array of holidays for tenant',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Holiday),
        },
      },
    },
  })
  async findByTenant(
    @param.path.string('tenantId') tenantId: string,
    @param.query.number('year') year?: number,
    @param.query.string('type') type?: string,
  ): Promise<Holiday[]> {
    const token = 'test-token'; // TODO: Extract from request headers
    return this.holidayFacadeService.getHolidaysByTenant(tenantId, token, year, type);
  }

  @get(`${basePath}/tenant/{tenantId}/upcoming`)
  @response(STATUS_CODES.OK, {
    description: 'Array of upcoming holidays for tenant',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Holiday),
        },
      },
    },
  })
  async findUpcoming(
    @param.path.string('tenantId') tenantId: string,
    @param.query.number('daysAhead') daysAhead?: number,
  ): Promise<Holiday[]> {
    const token = 'test-token'; // TODO: Extract from request headers
    return this.holidayFacadeService.getUpcomingHolidays(tenantId, token, daysAhead);
  }

  @patch(`${basePath}/{id}`)
  @response(STATUS_CODES.OK, {
    description: 'Holiday updated successfully',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Holiday),
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Holiday, {partial: true}),
        },
      },
    })
    holiday: Partial<Holiday>,
  ): Promise<Holiday> {
    const token = 'test-token'; // TODO: Extract from request headers
    return this.holidayFacadeService.updateHoliday(id, holiday, token);
  }

  @del(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'Holiday deleted successfully',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    const token = 'test-token'; // TODO: Extract from request headers
    await this.holidayFacadeService.deleteHoliday(id, token);
  }
}
