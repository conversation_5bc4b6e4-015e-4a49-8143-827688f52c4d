import {service} from '@loopback/core';
import {
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {authenticate} from '@loopback/authentication';
import {STRATEGY, CONTENT_TYPE, STATUS_CODES} from '@b6ai/core';
import {
  BusinessHoursCreateDto,
  BusinessHoursResponseDto,
  Department,
} from '../models';
import {BusinessHoursFacadeService} from '../services';

const basePath = '/business-hours';

@authenticate(STRATEGY.KEYCLOCK)
export class BusinessHoursController {
  constructor(
    @service(BusinessHoursFacadeService)
    private readonly businessHoursFacadeService: BusinessHoursFacadeService,
  ) {}

  @post(basePath)
  @response(STATUS_CODES.OK, {
    description: 'Business hours created successfully',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(BusinessHoursResponseDto),
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(BusinessHoursCreateDto, {
            title: 'NewBusinessHours',
          }),
        },
      },
    })
    businessHours: BusinessHoursCreateDto,
  ): Promise<BusinessHoursResponseDto> {
    const token = 'test-token'; // TODO: Extract from request headers
    return this.businessHoursFacadeService.createBusinessHours(businessHours, token);
  }

  @get(`${basePath}/tenant/{tenantId}`)
  @response(STATUS_CODES.OK, {
    description: 'Array of business hours for tenant',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(BusinessHoursResponseDto),
        },
      },
    },
  })
  async findByTenant(
    @param.path.string('tenantId') tenantId: string,
  ): Promise<BusinessHoursResponseDto[]> {
    const token = 'test-token'; // TODO: Extract from request headers
    return this.businessHoursFacadeService.getBusinessHoursByTenant(tenantId, token);
  }

  @get(`${basePath}/{id}`)
  @response(STATUS_CODES.OK, {
    description: 'Business hours model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(BusinessHoursResponseDto),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
  ): Promise<BusinessHoursResponseDto> {
    const token = 'test-token'; // TODO: Extract from request headers
    return this.businessHoursFacadeService.getBusinessHoursById(id, token);
  }

  @patch(`${basePath}/{id}`)
  @response(STATUS_CODES.OK, {
    description: 'Business hours updated successfully',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(BusinessHoursResponseDto),
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(BusinessHoursCreateDto, {partial: true}),
        },
      },
    })
    businessHours: Partial<BusinessHoursCreateDto>,
  ): Promise<BusinessHoursResponseDto> {
    const token = 'test-token'; // TODO: Extract from request headers
    return this.businessHoursFacadeService.updateBusinessHours(id, businessHours, token);
  }

  @del(`${basePath}/{id}`)
  @response(STATUS_CODES.NO_CONTENT, {
    description: 'Business hours deleted successfully',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    const token = 'test-token'; // TODO: Extract from request headers
    await this.businessHoursFacadeService.deleteBusinessHours(id, token);
  }

  @get('/departments')
  @response(STATUS_CODES.OK, {
    description: 'Array of departments',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Department),
        },
      },
    },
  })
  async getDepartments(): Promise<Department[]> {
    const token = 'test-token'; // TODO: Extract from request headers
    return this.businessHoursFacadeService.getDepartments(token);
  }

  @get('/departments/active')
  @response(STATUS_CODES.OK, {
    description: 'Array of active departments',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Department),
        },
      },
    },
  })
  async getActiveDepartments(): Promise<Department[]> {
    const token = 'test-token'; // TODO: Extract from request headers
    return this.businessHoursFacadeService.getActiveDepartments(token);
  }
}
