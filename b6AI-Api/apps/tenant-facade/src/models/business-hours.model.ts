import {Entity, model, property} from '@loopback/repository';

@model()
export class BusinessHours extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  tenantId: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: true,
  })
  timezone: string;

  @property({
    type: 'array',
    itemType: 'number',
    required: true,
  })
  workingDays: number[];

  @property({
    type: 'object',
    required: true,
  })
  workingHours: object;

  @property({
    type: 'object',
  })
  lunchBreak?: object;

  @property({
    type: 'boolean',
    default: false,
  })
  isDefault?: boolean;

  @property({
    type: 'string',
    default: 'active',
  })
  status?: string;

  @property({
    type: 'string',
  })
  createdOn?: string;

  @property({
    type: 'string',
  })
  modifiedOn?: string;

  constructor(data?: Partial<BusinessHours>) {
    super(data);
  }
}

export interface BusinessHoursRelations {
  // describe navigational properties here
}

export type BusinessHoursWithRelations = BusinessHours & BusinessHoursRelations;
