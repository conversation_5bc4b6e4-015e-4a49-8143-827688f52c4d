import {Entity, Model, model, property} from '@loopback/repository';

@model()
export class EmergencyContactInfo extends Model {
  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  phone?: string;

  @property({
    type: 'string',
  })
  email?: string;

  @property({
    type: 'string',
  })
  department?: string;

  constructor(data?: Partial<EmergencyContactInfo>) {
    super(data);
  }
}

@model()
export class OutOfOfficeSettings extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  tenantId: string;

  @property({
    type: 'string',
  })
  afterHoursMessage?: string;

  @property({
    type: 'string',
  })
  holidayMessage?: string;

  @property({
    type: 'boolean',
    default: false,
  })
  collectContactInfo?: boolean;

  @property({
    type: 'object',
  })
  emergencyContactInfo?: object;

  @property({
    type: 'string',
    default: 'active',
  })
  status?: string;

  @property({
    type: 'string',
  })
  createdOn?: string;

  @property({
    type: 'string',
  })
  modifiedOn?: string;

  constructor(data?: Partial<OutOfOfficeSettings>) {
    super(data);
  }
}

@model()
export class OutOfOfficeSettingsDto extends Model {
  @property({
    type: 'string',
    required: true,
  })
  tenantId: string;

  @property({
    type: 'string',
  })
  afterHoursMessage?: string;

  @property({
    type: 'string',
  })
  holidayMessage?: string;

  @property({
    type: 'boolean',
    default: false,
  })
  collectContactInfo?: boolean;

  @property({
    type: 'object',
  })
  emergencyContactInfo?: object;

  @property({
    type: 'string',
    default: 'active',
  })
  status?: string;

  constructor(data?: Partial<OutOfOfficeSettingsDto>) {
    super(data);
  }
}

export interface OutOfOfficeSettingsRelations {
  // describe navigational properties here
}

export type OutOfOfficeSettingsWithRelations = OutOfOfficeSettings &
  OutOfOfficeSettingsRelations;
