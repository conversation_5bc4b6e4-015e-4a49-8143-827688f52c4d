import {Model, model, property} from '@loopback/repository';

@model()
export class Idempotency extends Model {
  @property({
    type: 'string',
    required: true,
    id: true,
  })
  id: string;
  @property({
    type: 'string',
    required: true,
  })
  key: string;

  @property({
    type: 'string',
    required: true,
  })
  resourceType: string;

  @property({
    type: 'string',
  })
  resourceId?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'date',
    required: true,
  })
  createdAt: string;

  constructor(data?: Partial<Idempotency>) {
    super(data);
  }
}

export interface IdempotencyRelations {
  // describe navigational properties here
}

export type IdempotencyWithRelations = Idempotency & IdempotencyRelations;
