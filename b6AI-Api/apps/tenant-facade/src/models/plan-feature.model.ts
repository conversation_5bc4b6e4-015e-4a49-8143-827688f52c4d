import {Entity, model, property, belongsTo} from '@loopback/repository';
import {Feature} from './feature.model';
import {Plan} from './plan.model';

@model({
  name: 'plan_features',
})
export class PlanFeature extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
  })
  value: string;

  @belongsTo(() => Feature)
  featureId: string;

  @belongsTo(() => Plan)
  planId: string;

  constructor(data?: Partial<PlanFeature>) {
    super(data);
  }
}

export interface PlanFeatureRelations {
  // describe navigational properties here
}

export type PlanFeatureWithRelations = PlanFeature & PlanFeatureRelations;
