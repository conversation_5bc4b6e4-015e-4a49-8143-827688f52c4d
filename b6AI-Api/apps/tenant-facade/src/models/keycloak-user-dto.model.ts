import {Model, model, property} from '@loopback/repository';

@model()
export class KeycloakUserDto extends Model {
  @property({
    type: 'string',
    description: 'User ID',
  })
  id?: string;

  @property({
    type: 'string',
    description: 'Username',
  })
  username?: string;

  @property({
    type: 'string',
    description: 'Email address',
  })
  email?: string;

  @property({
    type: 'string',
    description: 'First name',
  })
  firstName?: string;

  @property({
    type: 'string',
    description: 'Last name',
  })
  lastName?: string;

  @property({
    type: 'boolean',
    description: 'Whether user is enabled',
  })
  enabled?: boolean;

  @property({
    type: 'number',
    description: 'User creation timestamp',
  })
  createdTimestamp?: number;

  @property({
    type: 'boolean',
    description: 'Whether email is verified',
  })
  emailVerified?: boolean;

  constructor(data?: Partial<KeycloakUserDto>) {
    super(data);
  }
}

export interface KeycloakUserDtoRelations {
  // describe navigational properties here
}

export type KeycloakUserDtoWithRelations = KeycloakUserDto & KeycloakUserDtoRelations;
