import {Model, model, property} from '@loopback/repository';
import {KeycloakUserDto} from './keycloak-user-dto.model';

@model()
export class OnboardResponseDto extends Model {
  @property({
    type: 'string',
    required: true,
    description: 'Response message',
  })
  message: string;

  @property({
    type: 'object',
    required: true,
    description: 'Created user information',
  })
  user: KeycloakUserDto;

  constructor(data?: Partial<OnboardResponseDto>) {
    super(data);
  }
}

export interface OnboardResponseDtoRelations {
  // describe navigational properties here
}

export type OnboardResponseDtoWithRelations = OnboardResponseDto &
  OnboardResponseDtoRelations;
