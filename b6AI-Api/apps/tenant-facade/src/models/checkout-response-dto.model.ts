import {Model, model, property} from '@loopback/repository';

@model()
export class CheckoutResponseDto extends Model {
  @property({
    type: 'string',
    required: true,
  })
  hostedPageUrl: string;


  constructor(data?: Partial<CheckoutResponseDto>) {
    super(data);
  }
}

export interface CheckoutResponseDtoRelations {
  // describe navigational properties here
}

export type CheckoutResponseDtoWithRelations = CheckoutResponseDto & CheckoutResponseDtoRelations;
