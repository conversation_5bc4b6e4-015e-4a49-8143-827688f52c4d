import {Model, model, property} from '@loopback/repository';

@model()
export class BusinessHoursResponseDto extends Model {
  @property({
    type: 'string',
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  tenantId: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: true,
  })
  timezone: string;

  @property({
    type: 'array',
    itemType: 'number',
    required: true,
  })
  workingDays: number[];

  @property({
    type: 'object',
    required: true,
  })
  workingHours: object;

  @property({
    type: 'object',
  })
  lunchBreak?: object;

  @property({
    type: 'boolean',
    default: false,
  })
  isDefault?: boolean;

  @property({
    type: 'string',
    default: 'active',
  })
  status?: string;

  @property({
    type: 'array',
    itemType: 'object',
  })
  departments?: any[];

  @property({
    type: 'string',
  })
  createdOn?: string;

  @property({
    type: 'string',
  })
  modifiedOn?: string;

  constructor(data?: Partial<BusinessHoursResponseDto>) {
    super(data);
  }
}

export interface BusinessHoursResponseDtoRelations {
  // describe navigational properties here
}

export type BusinessHoursResponseDtoWithRelations = BusinessHoursResponseDto &
  BusinessHoursResponseDtoRelations;
