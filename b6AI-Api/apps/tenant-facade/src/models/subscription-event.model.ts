import {Model, model, property} from '@loopback/repository';

@model()
class SubscriptionItem {
  @property({
    type: 'string',
    required: true,
  })
  item_price_id: string;

  @property({
    type: 'string',
    required: true,
  })
  item_type: string;

  @property({
    type: 'number',
    required: true,
  })
  quantity: number;

  @property({
    type: 'number',
    required: true,
  })
  unit_price: number;

  @property({
    type: 'number',
    required: true,
  })
  amount: number;

  @property({
    type: 'number',
    required: true,
  })
  free_quantity: number;

  @property({
    type: 'number',
  })
  trial_end?: number;

  @property({
    type: 'number',
  })
  billing_cycles?: number;

  @property({
    type: 'string',
  })
  object?: string;

  constructor(data?: Partial<SubscriptionItem>) {
    Object.assign(this, data);
  }
}

@model()
class Customer {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
  })
  first_name: string;

  @property({
    type: 'string',
    required: true,
  })
  last_name: string;

  @property({
    type: 'string',
    required: true,
  })
  email: string;

  @property({
    type: 'string',
  })
  company?: string;

  @property({
    type: 'string',
  })
  auto_collection?: string;

  @property({
    type: 'number',
  })
  net_term_days?: number;

  @property({
    type: 'boolean',
  })
  allow_direct_debit?: boolean;

  @property({
    type: 'number',
  })
  created_at?: number;

  @property({
    type: 'string',
  })
  created_from_ip?: string;

  @property({
    type: 'string',
  })
  taxability?: string;

  @property({
    type: 'number',
  })
  updated_at?: number;

  @property({
    type: 'string',
  })
  pii_cleared?: string;

  @property({
    type: 'string',
  })
  channel?: string;

  @property({
    type: 'number',
  })
  resource_version?: number;

  @property({
    type: 'boolean',
  })
  deleted?: boolean;

  @property({
    type: 'string',
  })
  object?: string;

  constructor(data?: Partial<Customer>) {
    Object.assign(this, data);
  }
}

@model()
class Subscription extends Model {
  @property({
    type: 'string',
    id: true,
    required: true,
  })
  id: string;

  @property({type: 'number'})
  billing_period?: number;

  @property({type: 'string'})
  billing_period_unit?: string;

  @property({type: 'number'})
  trial_end?: number;

  @property({type: 'number'})
  remaining_billing_cycles?: number;

  @property({type: 'string'})
  customer_id?: string;

  @property({type: 'string'})
  status?: string;

  @property({type: 'number'})
  trial_start?: number;

  @property({type: 'number'})
  next_billing_at?: number;

  @property({type: 'number'})
  created_at?: number;

  @property({type: 'number'})
  started_at?: number;

  @property({type: 'string'})
  created_from_ip?: string;

  @property({type: 'number'})
  updated_at?: number;

  @property({type: 'boolean'})
  has_scheduled_changes?: boolean;

  @property({type: 'string'})
  channel?: string;

  @property({type: 'number'})
  resource_version?: number;

  @property({type: 'boolean'})
  deleted?: boolean;

  @property({type: 'string'})
  object?: string;

  @property({type: 'string'})
  currency_code?: string;

  @property({
    type: 'array',
    itemType: 'object',
  })
  subscription_items: SubscriptionItem[];

  constructor(data?: Partial<SubscriptionEvent>) {
    super(data);
  }
}

@model()
export class SubscriptionEvent extends Model {
  @property({type: 'string', id: true})
  id?: string;

  @property({type: 'number'})
  occurred_at?: number;

  @property({type: 'string'})
  source?: string;

  @property({type: 'string'})
  object?: string;

  @property({type: 'string'})
  api_version?: string;

  @property({type: 'string'})
  event_type?: string;

  @property({
    type: 'object',
    required: true,
  })
  content: {subscription: Subscription; customer: Customer};

  [prop: string]: any;

  constructor(data?: Partial<SubscriptionEvent>) {
    super(data);
  }
}
