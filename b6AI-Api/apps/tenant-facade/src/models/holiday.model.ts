import {Entity, Model, model, property} from '@loopback/repository';

@model()
export class Holiday extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  tenantId: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'date',
    required: true,
  })
  date: Date;

  @property({
    type: 'string',
    default: 'global',
  })
  type?: string;

  @property({
    type: 'string',
  })
  countryCode?: string;

  @property({
    type: 'string',
    default: 'active',
  })
  status?: string;

  @property({
    type: 'string',
  })
  createdOn?: string;

  @property({
    type: 'string',
  })
  modifiedOn?: string;

  constructor(data?: Partial<Holiday>) {
    super(data);
  }
}

@model()
export class HolidayItem extends Model {
  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'date',
    required: true,
  })
  date: Date;

  @property({
    type: 'string',
    default: 'global',
  })
  type?: string;

  @property({
    type: 'string',
  })
  countryCode?: string;

  constructor(data?: Partial<HolidayItem>) {
    super(data);
  }
}

@model()
export class HolidayBulkCreateDto extends Model {
  @property({
    type: 'string',
    required: true,
  })
  tenantId: string;

  @property({
    type: 'array',
    itemType: HolidayItem,
    required: true,
  })
  holidays: HolidayItem[];

  constructor(data?: Partial<HolidayBulkCreateDto>) {
    super(data);
  }
}

export interface HolidayRelations {
  // describe navigational properties here
}

export type HolidayWithRelations = Holiday & HolidayRelations;
