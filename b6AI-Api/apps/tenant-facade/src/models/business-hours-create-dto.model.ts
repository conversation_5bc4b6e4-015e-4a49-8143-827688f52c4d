import {Model, model, property} from '@loopback/repository';

@model()
export class BusinessHoursCreateDto extends Model {
  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      minLength: 1,
      maxLength: 255,
      description: 'Tenant ID',
    },
  })
  tenantId: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      minLength: 1,
      maxLength: 100,
      description: 'Business hours configuration name',
    },
  })
  name: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      minLength: 1,
      maxLength: 50,
      pattern: '^[A-Za-z_]+/[A-Za-z_]+$',
      description: 'Timezone in IANA format (e.g., America/New_York)',
    },
  })
  timezone: string;

  @property({
    type: 'array',
    itemType: 'number',
    required: true,
    jsonSchema: {
      minItems: 1,
      maxItems: 7,
      items: {
        type: 'number',
        minimum: 1,
        maximum: 7,
      },
      description: 'Working days (1=Monday, 7=Sunday)',
    },
  })
  workingDays: number[];

  @property({
    type: 'object',
    required: true,
    jsonSchema: {
      description:
        'Working hours per day {"1": {"start": "09:00", "end": "17:00"}}',
    },
  })
  workingHours: object;

  @property({
    type: 'object',
    jsonSchema: {
      description: 'Lunch break hours {"start": "12:00", "end": "13:00"}',
    },
  })
  lunchBreak?: object;

  @property({
    type: 'boolean',
    default: false,
    jsonSchema: {
      description: 'Flag to mark as default configuration for tenant',
    },
  })
  isDefault?: boolean;

  @property({
    type: 'array',
    itemType: 'string',
    required: true,
    jsonSchema: {
      minItems: 1,
      items: {
        type: 'string',
        format: 'uuid',
      },
      description: 'Array of department IDs',
    },
  })
  departmentIds: string[];

  @property({
    type: 'string',
    default: 'active',
    jsonSchema: {
      enum: ['active', 'inactive'],
      description: 'Status of the business hours configuration',
    },
  })
  status?: string;

  constructor(data?: Partial<BusinessHoursCreateDto>) {
    super(data);
  }
}

export interface BusinessHoursCreateDtoRelations {
  // describe navigational properties here
}

export type BusinessHoursCreateDtoWithRelations = BusinessHoursCreateDto & BusinessHoursCreateDtoRelations;
