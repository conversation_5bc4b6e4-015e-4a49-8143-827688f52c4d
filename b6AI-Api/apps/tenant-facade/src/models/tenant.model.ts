import {SoftDeleteEntity} from '@b6ai/core';
import {model, property} from '@loopback/repository';

@model({name: 'tenants'})
export class Tenant extends SoftDeleteEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: true,
  })
  domain: string;

  @property({
    type: 'string',
    required: true,
  })
  status: string;

  @property({
    type: 'string',
    name: 'owner_id',
  })
  ownerId?: string;

  @property({
    type: 'string',
    name: 'contact_email',
  })
  contactEmail?: string;

  @property({
    type: 'string',
    name: 'contact_phone',
  })
  contactPhone?: string;

  @property({
    type: 'string',
  })
  address?: string;

  @property({
    type: 'object',
  })
  settings?: object;

  constructor(data?: Partial<Tenant>) {
    super(data);
  }
}

export interface TenantRelations {
  // describe navigational properties here
}

export type TenantWithRelations = Tenant & TenantRelations;
