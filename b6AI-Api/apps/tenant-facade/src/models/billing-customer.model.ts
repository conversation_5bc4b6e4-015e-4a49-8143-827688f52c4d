import {SoftDeleteEntity} from '@b6ai/core';
import {model, property} from '@loopback/repository';

@model({name: 'billing_customers'})
export class BillingCustomer extends SoftDeleteEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
    name: 'first_name',
  })
  firstName: string;

  @property({
    type: 'string',
    required: true,
    name: 'last_name',
  })
  lastName: string;

  @property({
    type: 'string',
    required: true,
  })
  email: string;

  @property({
    type: 'string',
  })
  company?: string;

  @property({
    type: 'string',
    name: 'tenant_id',
    required: true,
  })
  tenantId: string;

  @property({
    type: 'string',
    required: true,
    name: 'gateway_name',
    jsonSchema: {
      maxLength: 50,
      errorMessage: 'Gateway name must be <= 50 chars',
    },
  })
  gatewayName: string;

  @property({
    type: 'string',
    required: true,
    name: 'gateway_ref_id',
    jsonSchema: {
      maxLength: 100,
      errorMessage: 'Gateway reference ID must be <= 100 chars',
    },
  })
  gatewayRefId: string;

  constructor(data?: Partial<BillingCustomer>) {
    super(data);
  }
}

export interface BillingCustomerRelations {
  // describe navigational properties here
}

export type BillingCustomerWithRelations = BillingCustomer &
  BillingCustomerRelations;
