import {BootMixin} from '@loopback/boot';
import {ApplicationConfig} from '@loopback/core';
import {
  RestExplorerBindings,
  RestExplorerComponent,
} from '@loopback/rest-explorer';
import {RepositoryMixin} from '@loopback/repository';
import {Request, RestApplication} from '@loopback/rest';
import {ServiceMixin} from '@loopback/service-proxy';
import path from 'path';
import * as dotenv from 'dotenv';
import * as dotenvExt from 'dotenv-extended';
import {CACHE_DATA_SOURCE_NAME} from './constants';
import {
  RateLimiterComponent,
  RateLimitSecurityBindings,
} from 'loopback4-ratelimiter';
import {Tenant} from './models';
import {
  AuthenticationComponent,
  registerAuthenticationStrategy,
} from '@loopback/authentication';
import {CoreSequence, KeycloakStrategy, LocalCoreComponent} from '@b6ai/core';
import {ProxyBuilderBindings, ProxyBuilderComponent} from '@sourceloop/core';
import {
  BillingCustomer,
  Plan,
  Subscription,
  BusinessHours,
  Holiday,
  OutOfOfficeSettings,
  Department,
} from './models';
import {
  planProxyConfig,
  subscriptionProxyConfig,
  businessHoursProxyConfig,
  holidayProxyConfig,
  outOfOfficeSettingsProxyConfig,
  departmentProxyConfig,
} from './datasources/configs';
import {AuthServiceProxyProvider} from './services/auth-service-proxy.service';
export {ApplicationConfig};
const port = 3000;

const BaseApplication = BootMixin(
  ServiceMixin(RepositoryMixin(RestApplication)),
) as any;

export class TenantFacadeApplication extends BaseApplication {
  constructor(options: ApplicationConfig = {}) {
    dotenv.config();
    dotenvExt.load({
      schema: '.env.example',
      errorOnMissing: process.env.NODE_ENV !== 'test',
      includeProcessEnv: true,
    });

    options.rest = options.rest ?? {};
    options.rest.basePath = process.env.BASE_PATH ?? '';
    options.rest.port = +(process.env.PORT ?? port);
    options.rest.host = process.env.HOST;
    options.rest.openApiSpec = {
      endpointMapping: {
        [`${options.rest.basePath}/openapi.json`]: {
          version: '3.0.0',
          format: 'json',
        },
      },
    };

    options.rest.cors = {
      origin: process.env.CORS_ORIGIN?.split(',') || '*',
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      allowedHeaders: ['Content-Type', 'Authorization'],
      exposedHeaders: ['Content-Type', 'Authorization'],
      credentials: true,
      preflightContinue: false,
      optionsSuccessStatus: 204,
    };

    super(options);

    // Set up default home page
    this.static('/', path.join(__dirname, '../public'));

    this.configure(RestExplorerBindings.COMPONENT).to({
      path: '/explorer',
    });
    this.component(RestExplorerComponent);
    this.component(AuthenticationComponent);
    registerAuthenticationStrategy(this as any, KeycloakStrategy);

    this.component(RateLimiterComponent);
    this.bind(RateLimitSecurityBindings.CONFIG).to({
      name: CACHE_DATA_SOURCE_NAME,
      max: parseInt(process.env.RATE_LIMIT_REQUEST_CAP ?? '100'),
      keyGenerator: this.rateLimitKeyGen,
    });

    // Re-enable proxy builder config
    this.bind(ProxyBuilderBindings.CONFIG).to([
      {
        baseUrl: process.env.SUBSCRIPTION_SERVICE_URL,
        configs: [
          {model: Plan, basePath: '/plans', restOperations: planProxyConfig},
          {
            model: Subscription,
            basePath: '/subscriptions',
            restOperations: subscriptionProxyConfig,
          },
          {model: BillingCustomer, basePath: '/billing-customers'},
        ],
      },
      {
        baseUrl: process.env.TENANT_SERVICE_URL as string,
        configs: [
          {model: Tenant, basePath: '/tenants'},
          {
            model: BusinessHours,
            basePath: '/business-hours',
            restOperations: businessHoursProxyConfig,
          },
          {
            model: Holiday,
            basePath: '/holidays',
            restOperations: holidayProxyConfig,
          },
          {
            model: OutOfOfficeSettings,
            basePath: '/out-of-office-settings',
            restOperations: outOfOfficeSettingsProxyConfig,
          },
        ],
      },
      {
        baseUrl: process.env.AGENT_PORTAL_SERVICE_URL as string,
        configs: [
          {
            model: Department,
            basePath: '/departments',
            restOperations: departmentProxyConfig,
          },
        ],
      },
    ]);

    // Re-enable components step by step
    this.component(ProxyBuilderComponent);
    this.component(LocalCoreComponent);
    this.sequence(CoreSequence);

    // Re-enable AuthServiceProxy
    this.service(AuthServiceProxyProvider);

    this.projectRoot = __dirname;
    this.bootOptions = {
      controllers: {
        dirs: ['controllers'],
        extensions: ['.controller.js'],
        nested: true,
      },
    };
  }
  rateLimitKeyGen(req: Request) {
    const token = req.headers?.authorization?.replace(/bearer /i, '') ?? '';
    return token;
  }
}
