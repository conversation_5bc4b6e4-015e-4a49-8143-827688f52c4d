import {injectable, BindingScope, inject} from '@loopback/core';
import {restService} from '@sourceloop/core';
import {ILogger, LoggerBindings} from '@b6ai/core';
import {HttpErrors} from '@loopback/rest';
import {
  BusinessHours,
  BusinessHoursCreateDto,
  BusinessHoursResponseDto,
  Department,
} from '../models';
import {
  BusinessHoursProxyType,
  DepartmentProxyType,
  BusinessHoursResponseDto as ProxyBusinessHoursResponseDto,
  Department as ProxyDepartment,
} from '../datasources/configs';

@injectable({scope: BindingScope.TRANSIENT})
export class BusinessHoursFacadeService {
  constructor(
    @restService(BusinessHours)
    private readonly businessHoursProxy: BusinessHoursProxyType,
    @restService(Department)
    private readonly departmentProxy: DepartmentProxyType,
    @inject(LoggerBindings.LOGGER)
    private readonly logger: ILogger,
  ) {}

  async createBusinessHours(
    businessHoursData: BusinessHoursCreateDto,
    token: string,
  ): Promise<BusinessHoursResponseDto> {
    try {
      // Step 1: Validate departments exist in agent-portal-service
      await this.validateDepartments(businessHoursData.departmentIds, token);

      // Step 2: Create business hours in tenant-service
      const result = await this.businessHoursProxy.createBusinessHours(
        businessHoursData,
        token,
      );

      // Step 3: Convert and enrich response with department details
      const convertedResult = new BusinessHoursResponseDto(result);
      return this.enrichWithDepartmentDetails(convertedResult, token);
    } catch (error) {
      this.logger.error('Failed to create business hours', error);
      throw error;
    }
  }

  async updateBusinessHours(
    id: string,
    businessHoursData: Partial<BusinessHoursCreateDto>,
    token: string,
  ): Promise<BusinessHoursResponseDto> {
    try {
      // Step 1: Validate departments if provided
      if (businessHoursData.departmentIds) {
        await this.validateDepartments(businessHoursData.departmentIds, token);
      }

      // Step 2: Update business hours in tenant-service
      const result = await this.businessHoursProxy.updateBusinessHours(
        id,
        businessHoursData,
        token,
      );

      // Step 3: Convert and enrich response with department details
      const convertedResult = new BusinessHoursResponseDto(result);
      return this.enrichWithDepartmentDetails(convertedResult, token);
    } catch (error) {
      this.logger.error('Failed to update business hours', error);
      throw error;
    }
  }

  async getBusinessHoursByTenant(
    tenantId: string,
    token: string,
  ): Promise<BusinessHoursResponseDto[]> {
    try {
      // Get business hours from tenant-service
      const businessHoursList =
        await this.businessHoursProxy.getBusinessHoursByTenant(tenantId, token);

      // Enrich each business hours with department details
      const enrichedList: BusinessHoursResponseDto[] = [];
      for (const businessHours of businessHoursList) {
        const converted = new BusinessHoursResponseDto(businessHours);
        const enriched = await this.enrichWithDepartmentDetails(
          converted,
          token,
        );
        enrichedList.push(enriched);
      }

      return enrichedList;
    } catch (error) {
      this.logger.error('Failed to get business hours by tenant', error);
      throw error;
    }
  }

  async getBusinessHoursById(
    id: string,
    token: string,
  ): Promise<BusinessHoursResponseDto> {
    try {
      // Get business hours from tenant-service
      const businessHours = await this.businessHoursProxy.getBusinessHoursById(
        id,
        token,
      );

      // Convert and enrich with department details
      const converted = new BusinessHoursResponseDto(businessHours);
      return this.enrichWithDepartmentDetails(converted, token);
    } catch (error) {
      this.logger.error('Failed to get business hours by id', error);
      throw error;
    }
  }

  async deleteBusinessHours(id: string, token: string): Promise<void> {
    try {
      await this.businessHoursProxy.deleteBusinessHours(id, token);
    } catch (error) {
      this.logger.error('Failed to delete business hours', error);
      throw error;
    }
  }

  async getDepartments(token: string): Promise<Department[]> {
    try {
      const departments = await this.departmentProxy.getDepartments();
      return departments.map(dept => new Department(dept));
    } catch (error) {
      this.logger.error('Failed to get departments', error);
      throw error;
    }
  }

  async getActiveDepartments(token: string): Promise<Department[]> {
    try {
      const departments =
        await this.departmentProxy.getActiveDepartments(token);
      return departments.map(dept => new Department(dept));
    } catch (error) {
      this.logger.error('Failed to get active departments', error);
      throw error;
    }
  }

  private async validateDepartments(
    departmentIds: string[],
    token: string,
  ): Promise<void> {
    try {
      // Get all departments to validate IDs
      const allDepartments = await this.departmentProxy.getDepartments();
      console.log(
        '🚀 ~ BusinessHoursFacadeService ~ validateDepartments ~ allDepartments:',
        allDepartments,
      );
      const existingIds = allDepartments.map(dept => dept.id).filter(Boolean);

      const missingIds = departmentIds.filter(id => !existingIds.includes(id));

      if (missingIds.length > 0) {
        throw new HttpErrors.BadRequest(
          `Departments not found: ${missingIds.join(', ')}`,
        );
      }
    } catch (error) {
      if (error instanceof HttpErrors.HttpError) {
        throw error;
      }
      this.logger.error('Failed to validate departments', error);
      throw new HttpErrors.InternalServerError(
        'Failed to validate departments',
      );
    }
  }

  private async enrichWithDepartmentDetails(
    businessHours: BusinessHoursResponseDto,
    token: string,
  ): Promise<BusinessHoursResponseDto> {
    try {
      // If business hours already has departments, get their details
      if (businessHours.departments && businessHours.departments.length > 0) {
        const departmentIds = businessHours.departments
          .map(dept => dept.id)
          .filter(Boolean);

        if (departmentIds.length > 0) {
          const allDepartments = await this.departmentProxy.getDepartments();
          const relevantDepartments = allDepartments.filter(dept =>
            departmentIds.includes(dept.id),
          );

          businessHours.departments = relevantDepartments.map(
            dept => new Department(dept),
          );
        }
      }

      return businessHours;
    } catch (error) {
      this.logger.warn(
        'Failed to enrich with department details, returning original data',
        error,
      );
      return businessHours;
    }
  }
}
