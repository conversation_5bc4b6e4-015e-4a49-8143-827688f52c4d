import {injectable, BindingScope, service, inject} from '@loopback/core';
import {
  OnboardTenantDto,
  Tenant,
  OnboardResponseDto,
  KeycloakUserDto,
} from '../models';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {IdempotencyService} from './idempotency.service';
import {ILogger, LoggerBindings, TENANT_STATUS} from '@b6ai/core';
import {AuthServiceProxy, CreateUserData} from './auth-service-proxy.service';

@injectable({scope: BindingScope.TRANSIENT})
export class TenantService {
  constructor(
    @restService(Tenant)
    private readonly tenantProxy: ModifiedRestService<Tenant>,
    @service(IdempotencyService)
    private readonly idempotencyService: IdempotencyService,
    @inject(LoggerBindings.LOGGER)
    private readonly logger: ILogger,
    @inject('services.AuthServiceProxy')
    private readonly authServiceProxy: AuthServiceProxy,
  ) {}

  async onboard(tenant: OnboardTenantDto): Promise<OnboardResponseDto> {
    // Step 1: create idempotency record
    await this.idempotencyService.createKey(tenant.idempotencyKey, 'tenant');

    try {
      // Step 2: Validate unique customDomain before creating tenant
      try {
        // Check if domain already exists by trying to find it
        const existingTenants = await this.tenantProxy.find(
          {where: {domain: tenant.customDomain}},
          'test-token',
        );
        if (existingTenants && existingTenants.length > 0) {
          throw new Error(
            `Domain '${tenant.customDomain}' is already taken. Please choose a different domain.`,
          );
        }
      } catch (error: any) {
        // If it's not a "not found" error, re-throw it
        if (error.message.includes('already taken')) {
          throw error;
        }
        // Otherwise, continue (domain doesn't exist, which is good)
      }

      // Step 3: Call Tenant API
      const createdTenant = await this.tenantProxy.create(
        new Tenant({
          name: tenant.firstName + ' ' + tenant.lastName,
          domain: tenant.customDomain,
          status: TENANT_STATUS.PENDING,
          contactEmail: tenant.adminEmail,
        }),
        'test-token',
      );

      await this.idempotencyService.addResourceId(
        tenant.idempotencyKey,
        createdTenant.id,
      );

      // Step 3: Create Keycloak user with tenant admin credentials
      const userData: CreateUserData = {
        username: tenant.adminEmail, // Using admin email as username
        email: tenant.adminEmail,
        firstName: tenant.firstName,
        lastName: tenant.lastName,
        password: 'TempPassword123!', // Temporary password - should be reset on first login
        enabled: true,
      };

      const userResponse = await this.authServiceProxy.createUser(userData);

      // TODO : emit an event to orchestration service to create subdomain for the tenant

      return new OnboardResponseDto({
        message: `Tenant '${tenant.tenantName}' onboarded successfully. User created with temporary password.`,
        user: new KeycloakUserDto(userResponse.user),
      });
    } catch (error: any) {
      await this.rollbackTenant(tenant.idempotencyKey);

      // Handle database unique constraint errors
      if (error.message && error.message.includes('UniqueConstraintError')) {
        throw new Error(
          `Domain '${tenant.customDomain}' is already taken. Please choose a different domain.`,
        );
      }

      // Handle other domain validation errors
      if (error.message && error.message.includes('already taken')) {
        throw error;
      }

      this.logger.error('Failed to onboard tenant', error);
      throw error;
    }
  }

  async rollbackTenant(idempotencyKey: string) {
    const record = await this.idempotencyService.get(idempotencyKey);
    if (!record?.resourceId) return;
    try {
      await this.tenantProxy.deleteById(record.resourceId).catch(() => {});

      // TODO : find keycloak user by email and delete that user
      // TODO : remove subdomain from route 53
    } catch (e) {
      this.logger.error('Rollback partially failed', e);
    }
  }
}
