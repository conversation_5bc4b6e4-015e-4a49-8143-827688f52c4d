import {inject, Provider} from '@loopback/core';
import {getService} from '@loopback/service-proxy';
import {AuthServiceDataSource} from '../datasources/auth-service.datasource';

export interface CreateUserData {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  password?: string;
  enabled?: boolean;
}

export interface KeycloakUser {
  id?: string;
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  enabled?: boolean;
  createdTimestamp?: number;
  emailVerified?: boolean;
}

export interface UserResponse {
  message: string;
  user: KeycloakUser;
}

export interface AuthServiceProxy {
  createUser(data: CreateUserData): Promise<UserResponse>;
}

export class AuthServiceProxyProvider implements Provider<AuthServiceProxy> {
  constructor(
    @inject('datasources.authService')
    protected dataSource: AuthServiceDataSource = new AuthServiceDataSource(),
  ) {}

  value(): Promise<AuthServiceProxy> {
    return getService(this.dataSource);
  }
}
