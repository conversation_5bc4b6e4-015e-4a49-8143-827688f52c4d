import {injectable, BindingScope, inject} from '@loopback/core';
import {restService} from '@sourceloop/core';
import {ILogger, LoggerBindings} from '@b6ai/core';
import {OutOfOfficeSettings, OutOfOfficeSettingsDto} from '../models';
import {
  OutOfOfficeSettingsProxyType,
  OutOfOfficeSettings as ProxyOutOfOfficeSettings,
} from '../datasources/configs';

@injectable({scope: BindingScope.TRANSIENT})
export class OutOfOfficeFacadeService {
  constructor(
    @restService(OutOfOfficeSettings)
    private readonly outOfOfficeProxy: OutOfOfficeSettingsProxyType,
    @inject(LoggerBindings.LOGGER)
    private readonly logger: ILogger,
  ) {}

  async createOrUpdateSettings(
    settingsData: OutOfOfficeSettingsDto,
    token: string,
  ): Promise<OutOfOfficeSettings> {
    try {
      const result = await this.outOfOfficeProxy.createOrUpdateSettings(
        settingsData,
        token,
      );
      return new OutOfOfficeSettings(result as any);
    } catch (error) {
      this.logger.error(
        'Failed to create or update out-of-office settings',
        error,
      );
      throw error;
    }
  }

  async getSettingsByTenant(
    tenantId: string,
    token: string,
  ): Promise<OutOfOfficeSettings | null> {
    try {
      const result = await this.outOfOfficeProxy.getSettingsByTenant(
        tenantId,
        token,
      );
      return result ? new OutOfOfficeSettings(result as any) : null;
    } catch (error) {
      this.logger.error(
        'Failed to get out-of-office settings by tenant',
        error,
      );
      throw error;
    }
  }

  async getActiveSettings(
    tenantId: string,
    token: string,
  ): Promise<OutOfOfficeSettings | null> {
    try {
      const result = await this.outOfOfficeProxy.getActiveSettings(
        tenantId,
        token,
      );
      return result ? new OutOfOfficeSettings(result as any) : null;
    } catch (error) {
      this.logger.error('Failed to get active out-of-office settings', error);
      throw error;
    }
  }

  async updateSettings(
    id: string,
    settingsData: Partial<OutOfOfficeSettingsDto>,
    token: string,
  ): Promise<OutOfOfficeSettings> {
    try {
      const result = await this.outOfOfficeProxy.updateSettings(
        id,
        settingsData,
        token,
      );
      return new OutOfOfficeSettings(result as any);
    } catch (error) {
      this.logger.error('Failed to update out-of-office settings', error);
      throw error;
    }
  }

  async deleteSettings(id: string, token: string): Promise<void> {
    try {
      await this.outOfOfficeProxy.deleteSettings(id, token);
    } catch (error) {
      this.logger.error('Failed to delete out-of-office settings', error);
      throw error;
    }
  }
}
