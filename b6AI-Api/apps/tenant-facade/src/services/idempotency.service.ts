import {injectable, /* inject, */ BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {IdempotencyRepository} from '../repositories';
import {HttpErrors} from '@loopback/rest';

@injectable({scope: BindingScope.TRANSIENT})
export class IdempotencyService {
  constructor(
    @repository(IdempotencyRepository)
    private readonly repo: IdempotencyRepository,
  ) {}

  async createKey(key: string, resourceType: string) {
    const existing = await this.repo.get(key).catch(() => null);
    if (existing) {
      if (existing.status === 'completed') return existing; // already done
      throw new HttpErrors.BadRequest('Request is already in progress');
    }

    const idempotency = await this.repo.set(key, {
      key,
      resourceType,
      status: 'processing',
    });

    return idempotency;
  }

  async markCompleted(key: string, resourceId: string) {
    await this.repo.set(key, {
      status: 'completed',
      resourceId,
    });
  }

  async markFailed(key: string) {
    await this.repo.set(key, {
      status: 'failed',
    });
  }

  async addResourceId(key: string, resourceId: string) {
    await this.repo.set(key, {
      status: 'processing',
      resourceId,
    });
  }

  async get(key: string) {
    return this.repo.get(key).catch(() => null);
  }
}
