import {injectable, BindingScope, inject} from '@loopback/core';
import {
  BillingCustomer,
  Plan,
  Subscription,
  SubscriptionEvent,
} from '../models';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {
  GATEWAY_NAME,
  ILogger,
  LoggerBindings,
  PERIOD_UNIT,
  SUBSCRIPTION_STATUS,
} from '@b6ai/core';
import {PlanProxyType} from '../datasources/configs';
import {HttpErrors} from '@loopback/rest';

@injectable({scope: BindingScope.TRANSIENT})
export class WebhookService {
  constructor(
    @restService(Subscription)
    private readonly subscriptionProxy: ModifiedRestService<Subscription>,
    @restService(BillingCustomer)
    private readonly billingCustomerProxy: ModifiedRestService<BillingCustomer>,
    @restService(Plan)
    private readonly planProxy: PlanProxyType,
    @inject(LoggerBindings.LOGGER) private logger: ILogger,
  ) {}

  async handleSubscriptionEvent(event: SubscriptionEvent): Promise<void> {
    this.logger.info(
      `Webhook event from chargebee for event type ${event.event_type}`,
    );

    try {
      const {subscription, customer} = event.content;

      const token = 'test-token'; // TODO - generate internal token
      const [item] = subscription.subscription_items;
      const [itemCode] = item.item_price_id.split('_');
      const [plan] = await this.planProxy.find(
        {
          where: {planCode: itemCode},
          limit: 1,
          fields: {id: true},
        },
        token,
      );
      if (!plan) {
        HttpErrors.BadRequest(`Can not find the plan with code ${itemCode}`);
      }
      const billingCustomer = await this.billingCustomerProxy.create(
        new BillingCustomer({
          firstName: customer.first_name,
          lastName: customer.last_name,
          email: customer.email,
          company: customer.company ?? '',
          tenantId: customer.id,
          gatewayName: GATEWAY_NAME.CHARGEBEE,
          gatewayRefId: customer.id,
        }),
        token,
      );

      await this.subscriptionProxy.create(
        new Subscription({
          tenantId: billingCustomer.tenantId,
          status: subscription.status ?? SUBSCRIPTION_STATUS.ACTIVE,
          startDate: subscription.started_at
            ? new Date(subscription.started_at)
            : new Date(),
          trialEndDate: subscription.trial_end
            ? new Date(subscription.trial_end)
            : new Date(),
          renewalDate: subscription.next_billing_at
            ? new Date(subscription.next_billing_at)
            : new Date(),
          billingPeriodUnit: subscription.billing_period_unit as PERIOD_UNIT,
          billingPeriod: subscription.billing_period,
          gatewayName: GATEWAY_NAME.CHARGEBEE,
          gatewayRefId: subscription.id,
          planId: plan.id,
        }),
        token,
      );
    } catch (error) {
      this.logger.error(error, 'Webhook failed by error');
      throw error;
    }
  }
}
