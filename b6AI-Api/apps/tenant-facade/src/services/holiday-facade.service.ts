import {injectable, BindingScope, inject} from '@loopback/core';
import {restService} from '@sourceloop/core';
import {ILogger, LoggerBindings} from '@b6ai/core';
import {Holiday, HolidayBulkCreateDto} from '../models';
import {
  HolidayProxyType,
  Holiday as ProxyHoliday,
} from '../datasources/configs';

@injectable({scope: BindingScope.TRANSIENT})
export class HolidayFacadeService {
  constructor(
    @restService(Holiday)
    private readonly holidayProxy: HolidayProxyType,
    @inject(LoggerBindings.LOGGER)
    private readonly logger: ILogger,
  ) {}

  async createHoliday(
    holidayData: Omit<Holiday, 'id'>,
    token: string,
  ): Promise<Holiday> {
    try {
      const result = await this.holidayProxy.createHoliday(holidayData, token);
      return new Holiday(result);
    } catch (error) {
      this.logger.error('Failed to create holiday', error);
      throw error;
    }
  }

  async createHolidaysBulk(
    bulkData: HolidayBulkCreateDto,
    token: string,
  ): Promise<Holiday[]> {
    try {
      const results = await this.holidayProxy.createHolidaysBulk(
        bulkData,
        token,
      );
      return results.map(holiday => new Holiday(holiday));
    } catch (error) {
      this.logger.error('Failed to create holidays in bulk', error);
      throw error;
    }
  }

  async getHolidaysByTenant(
    tenantId: string,
    token: string,
    year?: number,
    type?: string,
  ): Promise<Holiday[]> {
    try {
      const results = await this.holidayProxy.getHolidaysByTenant(
        tenantId,
        token,
        year,
        type,
      );
      return results.map(holiday => new Holiday(holiday));
    } catch (error) {
      this.logger.error('Failed to get holidays by tenant', error);
      throw error;
    }
  }

  async getUpcomingHolidays(
    tenantId: string,
    token: string,
    daysAhead?: number,
  ): Promise<Holiday[]> {
    try {
      const results = await this.holidayProxy.getUpcomingHolidays(
        tenantId,
        token,
        daysAhead,
      );
      return results.map(holiday => new Holiday(holiday));
    } catch (error) {
      this.logger.error('Failed to get upcoming holidays', error);
      throw error;
    }
  }

  async updateHoliday(
    id: string,
    holidayData: Partial<Holiday>,
    token: string,
  ): Promise<Holiday> {
    try {
      const result = await this.holidayProxy.updateHoliday(
        id,
        holidayData,
        token,
      );
      return new Holiday(result);
    } catch (error) {
      this.logger.error('Failed to update holiday', error);
      throw error;
    }
  }

  async deleteHoliday(id: string, token: string): Promise<void> {
    try {
      await this.holidayProxy.deleteHoliday(id, token);
    } catch (error) {
      this.logger.error('Failed to delete holiday', error);
      throw error;
    }
  }
}
