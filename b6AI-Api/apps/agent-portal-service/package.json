{"name": "agent-portal-service", "version": "0.0.1", "description": "Departments, hours, routing rules, canned responses", "keywords": ["loopback-application", "loopback"], "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": "20 || 22 || 24"}, "scripts": {"build": "lb-tsc", "build:watch": "lb-tsc --watch", "lint": "npm run eslint && npm run prettier:check", "lint:fix": "npm run eslint:fix && npm run prettier:fix", "prettier:cli": "lb-prettier \"**/*.ts\" \"**/*.js\"", "prettier:check": "npm run prettier:cli -- -l", "prettier:fix": "npm run prettier:cli -- --write", "eslint": "lb-eslint --report-unused-disable-directives .", "eslint:fix": "npm run eslint -- --fix", "pretest": "npm run rebuild", "test": "lb-mocha --allow-console-logs \"dist/__tests__\"", "posttest": "npm run lint", "test:dev": "lb-mocha --allow-console-logs dist/__tests__/**/*.js && npm run posttest", "docker:build": "docker build -t agent-portal-service .", "docker:run": "docker run -p 3000:3000 -d agent-portal-service", "premigrate": "npm run build", "migrate": "node ./dist/migrate", "preopenapi-spec": "npm run build", "openapi-spec": "node ./dist/openapi-spec", "prestart": "npm run rebuild", "start": "node -r source-map-support/register .", "clean": "lb-clean dist *.tsbuildinfo .eslintcache", "rebuild": "npm run clean && npm run build"}, "repository": {"type": "git", "url": ""}, "author": "", "license": "", "files": ["README.md", "dist", "src", "!*/__tests__"], "dependencies": {"@b6ai/core": "workspace:*", "@loopback/authentication": "^12.0.6", "@loopback/boot": "^8.0.4", "@loopback/core": "^7.0.3", "@loopback/repository": "^8.0.3", "@loopback/rest": "^15.0.4", "@loopback/rest-explorer": "^8.0.4", "@loopback/security": "^0.12.5", "@loopback/sequelize": "^0.8.0", "dotenv-extended": "^2.9.0", "@loopback/service-proxy": "^8.0.3", "dotenv": "^17.2.2", "tslib": "^2.0.0"}, "devDependencies": {"@loopback/build": "^12.0.3", "@loopback/eslint-config": "^16.0.1", "@loopback/testlab": "^8.0.3", "@types/mocha": "^10.0.10", "@types/node": "^16.18.126", "eslint": "^8.57.1", "source-map-support": "^0.5.21", "typescript": "~5.2.2"}}