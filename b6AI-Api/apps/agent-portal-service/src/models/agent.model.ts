import {model, property} from '@loopback/repository';
import {SoftDeleteEntity} from '@b6ai/core';

@model({
  name: 'agents',
})
export class Agent extends SoftDeleteEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'department_id',
  })
  departmentId: string;

  @property({
    type: 'string',
    required: true,
    name: 'keycloak_id',
  })
  keycloakId: string;

  @property({
    type: 'string',
    name: 'profile_image',
  })
  profileImage?: string;

  constructor(data?: Partial<Agent>) {
    super(data);
  }
}

export interface AgentRelations {
  // describe navigational properties here
}

export type AgentWithRelations = Agent & AgentRelations;
