import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, AnyObject} from '@loopback/repository';
import {Department, DepartmentRelations} from '../models';
import {SecurityBindings} from '@loopback/security';
import {SoftDeleteBaseRepository} from '@b6ai/core';
import {PGDataSource} from '../datasources';

export class DepartmentRepository extends SoftDeleteBaseRepository<
  Department,
  typeof Department.prototype.id,
  DepartmentRelations
> {
  constructor(
    @inject('datasources.pg') dataSource: PGDataSource,
    @inject.getter(SecurityBindings.USER) getCurrentUser: Getter<AnyObject>,
  ) {
    super(Department, dataSource, getCurrentUser);
  }
}
