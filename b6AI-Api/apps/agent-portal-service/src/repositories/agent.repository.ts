import {Getter, inject} from '@loopback/core';
import {AnyObject} from '@loopback/repository';
import {Agent, AgentRelations} from '../models';
import {PGDataSource} from '../datasources';
import {SoftDeleteBaseRepository} from '@b6ai/core';
import {SecurityBindings} from '@loopback/security';

export class AgentRepository extends SoftDeleteBaseRepository<
  Agent,
  typeof Agent.prototype.id,
  AgentRelations
> {
  constructor(
    @inject('datasources.pg') dataSource: PGDataSource,
    @inject.getter(SecurityBindings.USER) getCurrentUser: Getter<AnyObject>,
  ) {
    super(Agent, dataSource, getCurrentUser);
  }
}
