import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Agent} from '../models';
import {AgentRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';
import {STRATEGY} from '@b6ai/core';

const basePath = '/agents';

@authenticate(STRATEGY.KEYCLOCK)
export class AgentController {
  constructor(
    @repository(AgentRepository)
    public agentRepository: AgentRepository,
  ) {}

  @post(basePath)
  @response(200, {
    description: 'Agent model instance',
    content: {'application/json': {schema: getModelSchemaRef(Agent)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Agent, {
            title: 'NewAgent',
            exclude: ['id'],
          }),
        },
      },
    })
    agent: Omit<Agent, 'id'>,
  ): Promise<Agent> {
    return this.agentRepository.create(agent);
  }

  @get(`${basePath}/count`)
  @response(200, {
    description: 'Agent model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(@param.where(Agent) where?: Where<Agent>): Promise<Count> {
    return this.agentRepository.count(where);
  }

  @get(basePath)
  @response(200, {
    description: 'Array of Agent model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Agent, {includeRelations: true}),
        },
      },
    },
  })
  async find(@param.filter(Agent) filter?: Filter<Agent>): Promise<Agent[]> {
    return this.agentRepository.find(filter);
  }

  @patch(basePath)
  @response(200, {
    description: 'Agent PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Agent, {partial: true}),
        },
      },
    })
    agent: Agent,
    @param.where(Agent) where?: Where<Agent>,
  ): Promise<Count> {
    return this.agentRepository.updateAll(agent, where);
  }

  @get(`${basePath}/{id}`)
  @response(200, {
    description: 'Agent model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Agent, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Agent, {exclude: 'where'})
    filter?: FilterExcludingWhere<Agent>,
  ): Promise<Agent> {
    return this.agentRepository.findById(id, filter);
  }

  @patch(`${basePath}/{id}`)
  @response(204, {
    description: 'Agent PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Agent, {partial: true}),
        },
      },
    })
    agent: Agent,
  ): Promise<void> {
    await this.agentRepository.updateById(id, agent);
  }

  @put(`${basePath}/{id}`)
  @response(204, {
    description: 'Agent PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() agent: Agent,
  ): Promise<void> {
    await this.agentRepository.replaceById(id, agent);
  }

  @del(`${basePath}/{id}`)
  @response(204, {
    description: 'Agent DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.agentRepository.deleteById(id);
  }
}
