{"name": "@b6-ai-api/source", "version": "0.0.0", "license": "MIT", "scripts": {"format": "prettier --write .", "lint": "eslint .", "build": "nx run-many -t build", "dev": "nx run-many -t dev --parallel=3", "test": "nx run-many -t test", "affected:build": "nx affected -t build", "affected:test": "nx affected -t test", "release": "standard-version"}, "private": true, "dependencies": {"core": "1.0.113"}, "devDependencies": {"@commitlint/cli": "^19.4.0", "@commitlint/config-conventional": "^19.4.0", "@nx/eslint": "^21.4.1", "@nx/js": "^21.4.1", "@nx/workspace": "21.4.1", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.9.0", "husky": "^9.1.5", "lint-staged": "^15.2.9", "nx": "21.4.1", "prettier": "^3.3.3", "standard-version": "^9.5.0", "typescript": "^5.5.4"}, "resolutions": {"@loopback/core": "^7.0.0"}}