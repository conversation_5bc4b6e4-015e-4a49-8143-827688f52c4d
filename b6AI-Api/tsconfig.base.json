{"compilerOptions": {"baseUrl": ".", "paths": {"@b6ai/core": ["packages/core/src/index.ts"]}, "emitDecoratorMetadata": true, "experimentalDecorators": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "strictPropertyInitialization": false, "useUnknownInCatchVariables": false, "incremental": true, "lib": ["es2020"], "module": "commonjs", "esModuleInterop": true, "moduleResolution": "node", "target": "es2018", "sourceMap": true, "declaration": true, "importHelpers": true, "composite": true}, "references": [{"path": "./packages/core"}], "exclude": ["node_modules", "dist"]}